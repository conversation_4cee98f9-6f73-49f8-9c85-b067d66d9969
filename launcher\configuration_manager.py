"""
Configuration Manager for Tower Defense Game Launcher
Handles loading, organizing, and managing game configurations
"""

import os
import json
from typing import Dict, List, Any
from datetime import datetime


class ConfigurationManager:
    """Manages loading and organizing game configurations"""
    
    def __init__(self):
        """Initialize the configuration manager"""
        self.configs = []        # Base level configs only
        self.variants = {}       # Variants organized by base level
        
    def load_configurations(self):
        """Load and organize configurations from organized folder structure"""
        base_config_dir = "config/base"
        variants_config_dir = "config/variants"
        
        self.configs = []        # Base level configs only
        self.variants = {}       # Variants organized by base level
        all_configs = []         # Temporary list for processing
        
        # Load base configs from config/base/
        if os.path.exists(base_config_dir):
            for filename in os.listdir(base_config_dir):
                if filename.endswith('.json'):
                    config_path = os.path.join(base_config_dir, filename)
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)
                        
                        # Determine config type
                        is_adaptive = '_adaptive_metadata' in config_data
                        is_variant = '_variant_metadata' in config_data
                        
                        # Extract metadata for display
                        config_info = {
                            'filename': filename,
                            'path': config_path,
                            'name': self.get_config_display_name(filename, config_data),
                            'difficulty': self.get_config_difficulty(config_data, filename),
                            'description': self.get_config_description(filename, config_data),
                            'is_adaptive': is_adaptive,
                            'is_variant': is_variant,
                            'variant_completed': False,
                            'variant_reward_multiplier': 1.0,
                            'creation_time': self.get_config_creation_time(config_path, config_data),
                            'level_metadata': self.get_config_level_metadata(config_data, filename),
                            'config_data': config_data
                        }
                        all_configs.append(config_info)
                        
                    except Exception as e:
                        print(f"Error loading base config {filename}: {e}")
        
        # Load variant configs from config/variants/
        if os.path.exists(variants_config_dir):
            for filename in os.listdir(variants_config_dir):
                if filename.endswith('.json'):
                    config_path = os.path.join(variants_config_dir, filename)
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)
                        
                        # Determine config type
                        is_adaptive = '_adaptive_metadata' in config_data
                        is_variant = '_variant_metadata' in config_data
                        
                        # For variants, get completion status
                        variant_completed = False
                        variant_reward_multiplier = 1.0
                        if is_variant and '_variant_metadata' in config_data:
                            metadata = config_data['_variant_metadata']
                            variant_id = metadata.get('variant_id', filename)
                            # Import here to avoid circular imports
                            from game_systems.level_variant_generator import LevelVariantGenerator
                            variant_generator = LevelVariantGenerator()
                            variant_completed = variant_generator._is_variant_completed(variant_id)
                            variant_reward_multiplier = metadata.get('reward_multiplier', 1.0)
                        
                        # Extract metadata for display
                        config_info = {
                            'filename': filename,
                            'path': config_path,
                            'name': self.get_config_display_name(filename, config_data),
                            'difficulty': self.get_config_difficulty(config_data, filename),
                            'description': self.get_config_description(filename, config_data),
                            'is_adaptive': is_adaptive,
                            'is_variant': is_variant,
                            'variant_completed': variant_completed,
                            'variant_reward_multiplier': variant_reward_multiplier,
                            'creation_time': self.get_config_creation_time(config_path, config_data),
                            'level_metadata': self.get_config_level_metadata(config_data, filename),
                            'config_data': config_data
                        }
                        all_configs.append(config_info)
                        
                    except Exception as e:
                        print(f"Error loading variant config {filename}: {e}")
        
        # Also check main config directory for adaptive/generated configs
        main_config_dir = "config"
        excluded_files = {
            'static_tower_config.json', 'static_balance_config.json', 'game_config.py',
            'README.md', 'tower_defense_game.json.backup', 'variant_completion.json'
        }
        
        if os.path.exists(main_config_dir):
            for filename in os.listdir(main_config_dir):
                if filename.endswith('.json') and filename not in excluded_files and not filename.endswith('.backup'):
                    config_path = os.path.join(main_config_dir, filename)
                    try:
                        with open(config_path, 'r') as f:
                            config_data = json.load(f)
                        
                        # Only include numbered adaptive configs (1.json, 2.json, etc.)
                        if filename.replace('.json', '').isdigit():
                            is_adaptive = '_adaptive_metadata' in config_data
                            
                            config_info = {
                                'filename': filename,
                                'path': config_path,
                                'name': self.get_config_display_name(filename, config_data),
                                'difficulty': self.get_config_difficulty(config_data, filename),
                                'description': self.get_config_description(filename, config_data),
                                'is_adaptive': is_adaptive,
                                'is_variant': False,
                                'variant_completed': False,
                                'variant_reward_multiplier': 1.0,
                                'creation_time': self.get_config_creation_time(config_path, config_data),
                                'level_metadata': self.get_config_level_metadata(config_data, filename),
                                'config_data': config_data
                            }
                            all_configs.append(config_info)
                            
                    except Exception as e:
                        print(f"Error loading adaptive config {filename}: {e}")
        
        # Organize configs by type
        for config in all_configs:
            if config['is_variant']:
                # This is a variant - organize by base level
                if '_variant_metadata' in config['config_data']:
                    metadata = config['config_data']['_variant_metadata']
                    base_level = metadata.get('base_level', 'unknown')
                else:
                    # Infer base level from filename for variants without metadata
                    filename = config['filename']
                    if filename.startswith('tower_defense_game_'):
                        base_level = 'tower_defense_game'
                    elif filename.startswith('test_config_'):
                        base_level = 'test_config'
                    else:
                        base_level = 'unknown'
                
                if base_level not in self.variants:
                    self.variants[base_level] = []
                self.variants[base_level].append(config)
                
            else:
                # This is a base level config or adaptive config
                self.configs.append(config)
        
        # Sort base configs by priority
        def sort_base_configs(config):
            filename = config['filename']
            # Priority 1: Numbered configs (1.json, 2.json, etc.)
            if filename.replace('.json', '').isdigit():
                return (0, int(filename.replace('.json', '')))
            # Priority 2: Named base configs
            else:
                return (1, config['creation_time'])
        
        self.configs.sort(key=sort_base_configs, reverse=True)
        
        # Sort variants within each base level
        for base_level in self.variants:
            self.variants[base_level].sort(key=lambda x: x['creation_time'], reverse=True)
    
    def get_config_display_name(self, filename: str, config_data: Dict) -> str:
        """Generate a user-friendly display name for the configuration"""
        
        # Check for AI-generated level name first
        if 'level_name' in config_data:
            return config_data['level_name']
        
        # Check for variant metadata name
        if '_variant_metadata' in config_data and 'variant_name' in config_data['_variant_metadata']:
            return config_data['_variant_metadata']['variant_name']
        
        # Check for level metadata name
        if 'level_metadata' in config_data and 'name' in config_data['level_metadata']:
            return config_data['level_metadata']['name']
        
        # Check if it's a numbered adaptive config
        if filename.replace('.json', '').isdigit():
            number = filename.replace('.json', '')
            if '_adaptive_metadata' in config_data:
                # Handle both old and new metadata structures
                if 'previous_performance' in config_data['_adaptive_metadata']:
                    score = config_data['_adaptive_metadata']['previous_performance'].get('score', 0)
                elif 'recent_performance_summary' in config_data['_adaptive_metadata']:
                    score = config_data['_adaptive_metadata']['recent_performance_summary'].get('average_score', 0)
                else:
                    score = 0
                return f"Level {number} (Adaptive - {score:.1f}% performance)"
            else:
                return f"Level {number}"
        
        # Handle named configs
        if filename == 'tower_defense_game.json':
            name = "Tower Defense Game"
        elif filename == 'test_config.json':
            name = "Test Config"
        else:
            name = filename.replace('.json', '').replace('_', ' ').title()
        
        # Add descriptive suffixes
        if 'adaptive' in filename.lower():
            return f"{name} (AI-Generated)"
        elif 'demo' in filename.lower():
            return f"{name} (Demo)"
        elif 'nightmare' in filename.lower():
            return f"{name} (Nightmare)"
        elif 'hard' in filename.lower():
            return f"{name} (Hard)"
        elif 'normal' in filename.lower():
            return f"{name} (Normal)"
        elif 'tutorial' in filename.lower():
            return f"{name} (Tutorial)"
        elif 'casual' in filename.lower():
            return f"{name} (Casual)"
        
        return name

    def get_config_difficulty(self, config_data: Dict, filename: str = "") -> int:
        """Extract difficulty rating from config data"""
        if '_generation_metadata' in config_data:
            diff = config_data['_generation_metadata'].get('difficulty', 50)
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif '_adaptive_metadata' in config_data:
            diff = config_data['_adaptive_metadata']['ai_adjustments']['difficulty_adjustment'].get('new_difficulty', 50)
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif 'calculated_difficulty' in config_data:
            # Check if it's a dict with 'score' field (like tower_defense_game.json)
            calc_diff = config_data.get('calculated_difficulty')
            if isinstance(calc_diff, dict):
                diff = calc_diff.get('score', 50)
            else:
                diff = calc_diff
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif 'game_config' in config_data and 'difficulty' in config_data['game_config']:
            # Check for difficulty in game_config section
            game_config = config_data['game_config']
            difficulty_value = game_config.get('difficulty')

            # Convert difficulty level names to scores
            if isinstance(difficulty_value, str):
                difficulty_map = {
                    'test': 1,
                    'tutorial': 10,
                    'easy': 20,
                    'casual': 25,
                    'normal': 50,
                    'medium': 60,
                    'hard': 70,
                    'very_hard': 85,
                    'nightmare': 95,
                    'impossible': 100
                }
                return difficulty_map.get(difficulty_value.lower(), 50)
            elif isinstance(difficulty_value, (int, float)):
                return int(difficulty_value)
        elif 'difficulty' in config_data:
            # Check for difficulty at root level
            diff_data = config_data.get('difficulty')
            if isinstance(diff_data, dict):
                # Check for score in difficulty object
                diff = diff_data.get('score', diff_data.get('level', 50))
            else:
                diff = diff_data

            # Convert difficulty level names to scores
            if isinstance(diff, str):
                difficulty_map = {
                    'test': 1,
                    'tutorial': 10,
                    'easy': 20,
                    'casual': 25,
                    'normal': 50,
                    'medium': 60,
                    'hard': 70,
                    'very_hard': 85,
                    'nightmare': 95,
                    'impossible': 100
                }
                return difficulty_map.get(diff.lower(), 50)
            elif isinstance(diff, (int, float)):
                return int(diff)
        else:
            # Guess based on filename
            filename_lower = filename.lower()
            if 'nightmare' in filename_lower:
                return 90
            elif 'hard' in filename_lower:
                return 70
            elif 'normal' in filename_lower:
                return 50
            elif 'tutorial' in filename_lower or 'easy' in filename_lower:
                return 30
            elif 'casual' in filename_lower:
                return 20
            elif 'test' in filename_lower:
                return 1
            else:
                return 50

        # Default fallback
        return 50

    def get_config_description(self, filename: str, config_data: Dict) -> str:
        """Generate a description of the configuration"""
        if '_variant_metadata' in config_data:
            # Variant config - show modifiers and multipliers
            metadata = config_data['_variant_metadata']
            base_level = metadata.get('base_level', 'Unknown')
            modifiers = metadata.get('modifiers', [])
            difficulty_mult = metadata.get('difficulty_multiplier', 1.0)
            reward_mult = metadata.get('reward_multiplier', 1.0)

            if modifiers:
                modifier_names = [mod.get('name', mod.get('id', 'Unknown')) for mod in modifiers]
                if len(modifier_names) == 1:
                    mod_desc = modifier_names[0]
                elif len(modifier_names) == 2:
                    mod_desc = f"{modifier_names[0]} + {modifier_names[1]}"
                else:
                    mod_desc = f"{len(modifier_names)} modifiers"
                return f"Variant of {base_level}: {mod_desc} ({difficulty_mult:.1f}x difficulty, {reward_mult:.1f}x reward)"
            else:
                return f"Variant of {base_level} with custom modifications"

        elif '_adaptive_metadata' in config_data:
            # Adaptive config - show what it was adapted for
            # Handle multiple metadata structures
            score = 0

            # New format: multi_game_context
            if 'multi_game_context' in config_data['_adaptive_metadata']:
                score = config_data['_adaptive_metadata']['multi_game_context'].get('avg_score', 0)
            # Old format: previous_performance
            elif 'previous_performance' in config_data['_adaptive_metadata']:
                performance = config_data['_adaptive_metadata']['previous_performance']
                score = performance.get('score', 0)
            # Alternative old format: recent_performance_summary
            elif 'recent_performance_summary' in config_data['_adaptive_metadata']:
                performance = config_data['_adaptive_metadata']['recent_performance_summary']
                score = performance.get('average_score', 0)

            reasoning = config_data['_adaptive_metadata']['ai_adjustments'].get('reasoning', '')

            # Truncate reasoning for display
            if len(reasoning) > 100:
                reasoning = reasoning[:100] + "..."

            return f"Generated for {score:.1f}% performance. {reasoning}"

        elif '_generation_metadata' in config_data:
            # Generated config
            meta = config_data['_generation_metadata']
            return f"Difficulty {meta.get('difficulty', 'Unknown')} - Procedurally generated level"

        else:
            # Manual/preset config
            if 'nightmare' in filename.lower():
                return "Ultimate challenge for expert players"
            elif 'hard' in filename.lower():
                return "Challenging gameplay for experienced players"
            elif 'normal' in filename.lower():
                return "Balanced gameplay for most players"
            elif 'tutorial' in filename.lower():
                return "Learning experience for new players"
            elif 'casual' in filename.lower():
                return "Relaxed gameplay for casual sessions"
            elif 'demo' in filename.lower():
                return "Demonstration of game features"
            else:
                return "Custom configuration"

    def get_config_creation_time(self, config_path: str, config_data: Dict) -> float:
        """Get the creation time of the configuration"""
        # Try to get timestamp from adaptive metadata
        if '_adaptive_metadata' in config_data:
            timestamp_str = config_data['_adaptive_metadata'].get('generation_timestamp')
            if timestamp_str:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    return dt.timestamp()
                except:
                    pass

        # Fall back to file modification time
        try:
            return os.path.getmtime(config_path)
        except:
            return 0

    def get_config_level_metadata(self, config_data: Dict, filename: str) -> Dict:
        """Extract level metadata from config data"""
        # Check if config has level_metadata section
        if 'level_metadata' in config_data:
            return config_data['level_metadata']

        # Generate metadata from config info
        difficulty_score = self.get_config_difficulty(config_data, filename)

        # Calculate victory points using the same formula as GlobalUpgradeSystem
        if difficulty_score <= 1:
            victory_points = 1
        elif difficulty_score >= 100:
            victory_points = 15
        else:
            victory_points = 1 + int((difficulty_score - 1) * (14 / 99))

        # Generate difficulty name
        difficulty_name = "Unknown"
        if difficulty_score <= 10:
            difficulty_name = "Very Easy"
        elif difficulty_score <= 30:
            difficulty_name = "Easy"
        elif difficulty_score <= 50:
            difficulty_name = "Normal"
        elif difficulty_score <= 70:
            difficulty_name = "Hard"
        elif difficulty_score <= 85:
            difficulty_name = "Very Hard"
        else:
            difficulty_name = "Nightmare"

        # Generate default metadata
        return {
            "name": self.get_config_display_name(filename, config_data),
            "description": f"A {difficulty_name.lower()} challenge that will test your strategic skills. Prepare your defenses and adapt your tactics to overcome the incoming waves of enemies.",
            "difficulty_rating": difficulty_name,
            "estimated_duration": "30-45 minutes",
            "recommended_for": "all players",
            "special_features": [
                f"Difficulty rating: {difficulty_score}/100",
                "Dynamic enemy scaling",
                "Strategic tower placement required"
            ],
            "victory_rewards": {
                "victory_points": victory_points,
                "victory_points_description": f"Earn {victory_points} Victory Points for completing this level",
                "unlock_requirements": [],
                "completion_bonuses": [
                    "Tower upgrade points",
                    "Strategic experience",
                    "Achievement progress"
                ]
            },
            "tips": [
                "Study enemy types and their weaknesses",
                "Balance offense and economy",
                "Adapt your strategy as waves progress",
                "Use terrain to your advantage"
            ]
        }

    def get_base_level_for_config(self, config):
        """Extract base level name from a config"""
        if config.get('is_variant'):
            metadata = config.get('config_data', {}).get('_variant_metadata', {})
            return metadata.get('base_level', 'unknown')
        else:
            # For base configs, derive base level name from filename
            filename = config['filename']
            if filename == 'tower_defense_game.json':
                return 'tower_defense_game'
            elif filename == 'test_config.json':
                return 'test_config'
            elif filename.replace('.json', '').isdigit():
                return f"level_{filename.replace('.json', '')}"
            else:
                return filename.replace('.json', '').split('_')[0]

    def get_current_config_list(self, navigation_manager):
        """Get the current list of configs to display based on view"""
        if navigation_manager.current_view == "main":
            return self.configs
        else:  # variants view
            base_level = navigation_manager.selected_base_level
            return self.variants.get(base_level, [])
