"""
Difficulty Factors Module for Tower Defense Config Generation

Contains difficulty calculation and factor derivation logic.
"""

from typing import Dict, <PERSON>, Tuple, Any
import json
import os

# Load the complete tower defense game configuration
def load_tower_defense_config() -> Dict[str, Any]:
    """Load the complete tower defense game configuration"""
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'base', 'tower_defense_game.json')
    with open(config_path, 'r') as f:
        return json.load(f)

# Global configuration reference
TOWER_DEFENSE_CONFIG = load_tower_defense_config()

class DifficultyKnowledge:
    """Comprehensive knowledge about tower defense game difficulty"""
    
    def __init__(self):
        self.config = TOWER_DEFENSE_CONFIG
        self.difficulty_70_analysis = self._analyze_difficulty_70()
    
    def _analyze_difficulty_70(self) -> Dict[str, Any]:
        """Analyze what difficulty 70 means in the tower defense game"""
        return {
            "score": 70,
            "description": "Hard - Challenging configuration requiring strategy",
            "key_characteristics": {
                "starting_resources": {
                    "money": 20,
                    "lives": 20,
                    "difficulty_impact": "Very Low - Maximum difficulty points (25.0)",
                    "challenge": "Extremely limited starting resources force careful tower placement"
                },
                "enemy_scaling": {
                    "health_per_wave": 0.3,
                    "max_health_multiplier": 60.0,
                    "difficulty_impact": "High (22.5 points)",
                    "challenge": "Enemies become 60x stronger by end game (can be increased by AI for harder difficulties)"
                },
                "spawn_progression": {
                    "enemy_increase_per_round": "1-16 enemies added per wave",
                    "spawn_delay_reduction": "5-22 frames faster per wave",
                    "difficulty_impact": "Moderate (10.3 points)",
                    "challenge": "Rapid enemy spawning with minimal delays"
                },
                "special_rounds": {
                    "multipliers": [1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 6.0, 8.0],
                    "difficulty_impact": "Strong (8.6 points)",
                    "challenge": "Massive enemy surges every 10 waves"
                },
                "tower_economics": {
                    "cost_progression": "Up to 3x cost multiplier",
                    "dynamic_increase": "15% per tower built",
                    "difficulty_impact": "Moderate (4.0 points)",
                    "challenge": "Towers become prohibitively expensive"
                }
            },
            "wave_structure": {
                "total_waves": 80,
                "boss_waves": 12,
                "boss_density": "High - 15% of waves are boss waves",
                "boss_types": ["SpeedBoss", "MegaBoss", "NecromancerBoss", "TimeLordBoss", "ShadowKing", "CrystalOverlord"]
            },
            "enemy_composition": {
                "early_game": "Basic, Fast, Tank enemies",
                "mid_game": "Flying, Shielded, Invisible, Armored enemies",
                "late_game": "Spectral, Crystalline, ToxicMutant, Void, Adaptive enemies",
                "immunity_system": "Enemies can develop immunities to specific towers"
            },
            "tower_balance": {
                "base_costs": "20-120 money range",
                "counter_system": "Towers have specific strengths/weaknesses vs enemy types",
                "upgrade_system": "Towers can be upgraded for improved performance"
            }
        }
    
    def get_difficulty_70_reference(self) -> Dict[str, Any]:
        """Get the complete reference for what difficulty 70 means"""
        return self.difficulty_70_analysis
    
    def understand_difficulty_components(self) -> Dict[str, Any]:
        """Understand how different components contribute to difficulty"""
        return {
            "starting_resources": {
                "description": "Initial money and lives available",
                "difficulty_70_value": "20 money, 20 lives",
                "impact": "Higher money = helps with harder challenges, Higher lives = easier game",
                "adjustment_range": "20-100 money, 10-50 lives"
            },
            "enemy_scaling": {
                "description": "How much enemies strengthen per wave",
                "difficulty_70_value": "30% health per wave, 60x max multiplier",
                "impact": "Higher values = harder game",
                "adjustment_range": "10%-50% health per wave, 20x-100x max multiplier"
            },
            "spawn_progression": {
                "description": "How quickly enemy spawning increases",
                "difficulty_70_value": "1-16 enemies added per wave, 5-22 frame delay reduction",
                "impact": "Higher values = harder game",
                "adjustment_range": "0-20 enemies added, 0-30 frame reduction"
            },
            "special_rounds": {
                "description": "Intensity of special wave multipliers",
                "difficulty_70_value": "1.5x to 8.0x enemy multipliers",
                "impact": "Higher multipliers = harder game",
                "adjustment_range": "1.2x to 10.0x multipliers"
            },
            "tower_economics": {
                "description": "How expensive towers become over time",
                "difficulty_70_value": "3x max cost multiplier, 15% per tower built",
                "impact": "Higher costs = harder game",
                "adjustment_range": "1.5x to 5x multiplier, 5%-25% per tower"
            }
        }
    
    def get_enemy_type_difficulty(self) -> Dict[str, int]:
        """Get difficulty rating for each enemy type (1-10 scale)"""
        return {
            "BasicEnemy": 1,
            "FastEnemy": 2,
            "TankEnemy": 3,
            "FlyingEnemy": 4,
            "ShieldedEnemy": 5,
            "InvisibleEnemy": 6,
            "ArmoredEnemy": 4,
            "RegeneratingEnemy": 5,
            "TeleportingEnemy": 6,
            "SplittingEnemy": 7,
            "FireElementalEnemy": 6,
            "ToxicEnemy": 5,
            "PhaseShiftEnemy": 7,
            "BlastProofEnemy": 6,
            "SpectralEnemy": 8,
            "CrystallineEnemy": 8,
            "ToxicMutantEnemy": 7,
            "VoidEnemy": 8,
            "AdaptiveEnemy": 9,
            "SpeedBoss": 6,
            "MegaBoss": 7,
            "NecromancerBoss": 8,
            "TimeLordBoss": 8,
            "ShadowKing": 9,
            "CrystalOverlord": 10
        }
    
    def get_tower_effectiveness_matrix(self) -> Dict[str, Dict[str, float]]:
        """Get tower effectiveness against different enemy types"""
        return self.config["balance_config"]["counter_system"]["tower_enemy_multipliers"]
    
    def calculate_target_difficulty_adjustment(self, current_difficulty: float, target_difficulty: float, 
                                            recent_performance: List[float]) -> Dict[str, float]:
        """Calculate how to adjust difficulty factors to reach target difficulty"""
        
        # Understand what the target difficulty means relative to current
        if target_difficulty > current_difficulty:
            # Making it harder than current difficulty
            direction = "increase"
            reference = f"harder than current difficulty {current_difficulty}"
        elif target_difficulty < current_difficulty:
            # Making it easier than current difficulty
            direction = "decrease"
            reference = f"easier than current difficulty {current_difficulty}"
        else:
            # Same difficulty level
            direction = "maintain"
            reference = f"same as current difficulty {current_difficulty}"
        
        # Calculate adjustment factors based on performance
        avg_performance = sum(recent_performance) / len(recent_performance) if recent_performance else 0.5
        
        # Determine adjustment intensity
        if abs(target_difficulty - current_difficulty) > 20:
            intensity = "major"
        elif abs(target_difficulty - current_difficulty) > 10:
            intensity = "moderate"
        else:
            intensity = "minor"
        
        # Calculate specific adjustments
        adjustments = {}
        
        # Calculate difficulty change
        difficulty_change = target_difficulty - current_difficulty
        
        if direction == "increase":
            # Making it harder - INCREASE starting money to help with harder challenges
            adjustments = {
                "starting_money": max(20, 20 + difficulty_change * 0.8),
                "starting_lives": max(10, 20 - difficulty_change * 0.3),
                "enemy_health_scaling": min(0.5, 0.3 + difficulty_change * 0.005),
                "spawn_delay_reduction": min(30, 22 + difficulty_change * 0.2),
                "special_round_multipliers": [1.5 + difficulty_change * 0.05] * 8,
                "tower_cost_multiplier": min(5.0, 3.0 + difficulty_change * 0.05)
            }
        elif direction == "decrease":
            # Making it easier - DECREASE starting money since challenges are easier
            adjustments = {
                "starting_money": max(20, 20 + difficulty_change * 0.5),  # difficulty_change is negative here
                "starting_lives": min(50, 20 - difficulty_change * 0.5),  # difficulty_change is negative here
                "enemy_health_scaling": max(0.1, 0.3 + difficulty_change * 0.005),  # difficulty_change is negative here
                "spawn_delay_reduction": max(5, 22 + difficulty_change * 0.3),  # difficulty_change is negative here
                "special_round_multipliers": [max(1.2, 1.5 + difficulty_change * 0.02)] * 8,  # difficulty_change is negative here
                "tower_cost_multiplier": max(1.5, 3.0 + difficulty_change * 0.05)  # difficulty_change is negative here
            }
        else:
            # Same difficulty level - use difficulty 70 as reference
            adjustments = {
                "starting_money": 20,
                "starting_lives": 20,
                "enemy_health_scaling": 0.3,
                "spawn_delay_reduction": 22,
                "special_round_multipliers": [1.5, 2.0, 2.5, 3.0, 4.0, 5.0, 6.0, 8.0],
                "tower_cost_multiplier": 3.0
            }
        
        return {
            "target_difficulty": target_difficulty,
            "direction": direction,
            "intensity": intensity,
            "reference": reference,
            "adjustments": adjustments,
            "performance_context": f"Recent performance average: {avg_performance:.2f}"
        }

# Global difficulty knowledge instance
DIFFICULTY_KNOWLEDGE = DifficultyKnowledge()

def get_difficulty_70_reference() -> Dict[str, Any]:
    """Get the complete reference for what difficulty 70 means"""
    return DIFFICULTY_KNOWLEDGE.get_difficulty_70_reference()

def understand_difficulty_components() -> Dict[str, Any]:
    """Understand how different components contribute to difficulty"""
    return DIFFICULTY_KNOWLEDGE.understand_difficulty_components()

def get_enemy_type_difficulty() -> Dict[str, int]:
    """Get difficulty rating for each enemy type (1-10 scale)"""
    return DIFFICULTY_KNOWLEDGE.get_enemy_type_difficulty()

def get_tower_effectiveness_matrix() -> Dict[str, Dict[str, float]]:
    """Get tower effectiveness against different enemy types"""
    return DIFFICULTY_KNOWLEDGE.get_tower_effectiveness_matrix()

def calculate_target_difficulty_adjustment(current_difficulty: float, target_difficulty: float, 
                                        recent_performance: List[float]) -> Dict[str, Any]:
    """Calculate how to adjust difficulty factors to reach target difficulty"""
    return DIFFICULTY_KNOWLEDGE.calculate_target_difficulty_adjustment(current_difficulty, target_difficulty, recent_performance)

def derive_difficulty_factors(difficulty: int) -> Dict[str, float]:
    """
    Derive base factors from difficulty D ∈ [0,100]
    
    Args:
        difficulty: Raw difficulty parameter from 0 to 100
        
    Returns:
        Dictionary containing normalized factors for config generation
    """
    # Clamp to valid range
    D = max(0, min(100, difficulty))
    
    # Normalized difficulty for various calculations [0,1]
    normalized_difficulty = D / 100.0
    
    # Base enemy count: scales from 5 to 25 enemies per wave initially
    base_count = 5 + (20 * normalized_difficulty)
    
    # Base spawn delay: scales from 120 frames (easy) to 30 frames (hard)
    base_delay = 120 - (90 * normalized_difficulty)
    
    # Map complexity: affects path twists and turns [0,1] 
    complexity = 0.3 + (0.7 * normalized_difficulty)
    
    # FIXED: Buildable space percentage (MORE space = HARDER decisions)
    # Easy: 80% buildable, Hard: 35% buildable
    buildable_space = 0.8 - (0.45 * normalized_difficulty)
    
    # Strategic terrain density: forces specific tower types [0,1]
    strategic_terrain_density = 0.1 + (0.6 * normalized_difficulty)
    
    # Enemy scaling factors (how much they grow per wave)
    health_scale = 0.08 + (0.15 * normalized_difficulty)  # 8-23% health per wave
    speed_scale = 0.03 + (0.05 * normalized_difficulty)   # 3-8% speed per wave
    
    return {
        'difficulty': D,
        'normalized_difficulty': normalized_difficulty,
        'base_count': base_count,
        'base_delay': base_delay,
        'complexity': complexity,
        'buildable_space': buildable_space,
        'strategic_terrain_density': strategic_terrain_density,
        'health_scale': health_scale,
        'speed_scale': speed_scale,
        # Legacy support for old systems
        'obstacle_density': 1.0 - buildable_space  # For backwards compatibility
    }


def calculate_wave_parameters(wave_num: int, difficulty_factors: Dict[str, float], 
                            total_waves: int = 80) -> Dict[str, any]:
    """
    Compute parameters for a specific wave
    
    Args:
        wave_num: Wave number (1-based)
        difficulty_factors: Derived difficulty factors
        total_waves: Total number of waves
        
    Returns:
        Dictionary with wave parameters
    """
    D = difficulty_factors['difficulty']
    base_count = difficulty_factors['base_count']
    base_delay = difficulty_factors['base_delay']
    health_scale = difficulty_factors['health_scale']
    speed_scale = difficulty_factors['speed_scale']
    
    # Calculate count
    enemy_increase = lookup_enemy_increase(wave_num)
    count = int(base_count + enemy_increase * (1 + 0.5 * (D / 100)) * (wave_num - 1))
    
    # Calculate delay
    delay_reduction = lookup_delay_reduction(wave_num)
    delay = max(20, int(base_delay - delay_reduction * (1 + 0.5 * (D / 100))))
    
    # Calculate multipliers
    max_hp = 60.0
    max_spd = 5.0
    hp_mult = min(max_hp, 1 + health_scale * (wave_num - 1))
    spd_mult = min(max_spd, 1 + speed_scale * (wave_num - 1))
    
    # Apply special round multipliers
    special_multipliers = {
        10: {'enemy_multiplier': 1.5, 'spawn_delay_multiplier': 0.8},
        20: {'enemy_multiplier': 2.0, 'spawn_delay_multiplier': 0.7},
        30: {'enemy_multiplier': 2.5, 'spawn_delay_multiplier': 0.6},
        40: {'enemy_multiplier': 3.0, 'spawn_delay_multiplier': 0.5},
        50: {'enemy_multiplier': 4.0, 'spawn_delay_multiplier': 0.4},
        60: {'enemy_multiplier': 5.0, 'spawn_delay_multiplier': 0.3},
        70: {'enemy_multiplier': 6.0, 'spawn_delay_multiplier': 0.25},
        80: {'enemy_multiplier': 8.0, 'spawn_delay_multiplier': 0.2}
    }
    
    if wave_num in special_multipliers:
        count = int(count * special_multipliers[wave_num]['enemy_multiplier'])
        delay = int(delay * special_multipliers[wave_num]['spawn_delay_multiplier'])
    
    # Check for boss waves
    boss_waves = {20: "TimeLordBoss", 25: "ShadowKing", 30: "NecromancerBoss", 
                 35: "CrystalOverlord", 40: "TimeLordBoss", 45: "ShadowKing",
                 50: "NecromancerBoss", 55: "CrystalOverlord", 60: "TimeLordBoss",
                 65: "ShadowKing", 70: "NecromancerBoss", 75: "CrystalOverlord", 
                 80: "TimeLordBoss"}
    
    boss = boss_waves.get(wave_num)
    
    return {
        'enemy_count': count,
        'spawn_delay': delay,
        'hp_multiplier': hp_mult,
        'speed_multiplier': spd_mult,
        'boss': boss
    }


def lookup_enemy_increase(wave: int) -> int:
    """Get enemy count increase for wave"""
    if wave <= 5:
        return 1
    elif wave <= 10:
        return 2
    elif wave <= 15:
        return 3
    elif wave <= 20:
        return 4
    elif wave <= 25:
        return 5
    elif wave <= 30:
        return 6
    elif wave <= 35:
        return 7
    elif wave <= 40:
        return 8
    elif wave <= 45:
        return 9
    elif wave <= 50:
        return 10
    elif wave <= 55:
        return 11
    elif wave <= 60:
        return 12
    elif wave <= 65:
        return 13
    elif wave <= 70:
        return 14
    elif wave <= 75:
        return 15
    else:
        return 16


def lookup_delay_reduction(wave: int) -> int:
    """Get spawn delay reduction for wave"""
    if wave <= 10:
        return 5
    elif wave <= 20:
        return 8
    elif wave <= 30:
        return 10
    elif wave <= 40:
        return 12
    elif wave <= 50:
        return 15
    elif wave <= 60:
        return 18
    elif wave <= 70:
        return 20
    else:
        return 22 