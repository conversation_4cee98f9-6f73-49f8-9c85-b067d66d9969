from .tower import Tower
import pygame
import math
from projectiles.projectile import Projectile

class ExplosiveTower(Tower):
    """Tower that fires explosive rockets with large splash damage"""
    
    def __init__(self, x, y):
        super().__init__(x, y, 'explosive')
        self.damage = 60  # MASSIVE damage boost (was 35)
        self.range = 300  # MUCH longer range (was 180)
        self.fire_rate = 80  # Faster firing (was 100)
        self.projectile_speed = 4.5  # Faster projectiles
        self.size = 16  # Larger tower
        self.color = (255, 165, 0)  # Orange
        
        # Explosive properties - SIGNIFICANTLY BOOSTED
        self.splash_radius = 100  # HUGE explosion area (was 60)
        self.splash_damage = 35  # High splash damage (was 20)
        
        # POWERFUL SIEGE WEAPON - can target both ground and air
        self.can_target_flying = True  # Now targets flying enemies too!
        self.can_target_invisible = False
        
        # CRITICAL: Finalize initialization to set base stats correctly
        self.finalize_initialization()
    
    def can_target_enemy(self, enemy):
        """Check if this tower can target a specific enemy"""
        # First check parent class immunity
        if not super().can_target_enemy(enemy):
            return False
        
        if hasattr(enemy, 'flying') and enemy.flying and not self.can_target_flying:
            return False
        # Can target invisible enemies if they've been detected by a detector tower
        if hasattr(enemy, 'invisible') and enemy.invisible and not self.can_target_invisible:
            if not hasattr(enemy, 'detected_by_detector') or not enemy.detected_by_detector:
                return False
        return True
    
    def acquire_target(self, enemies):
        """Find target using targeting restrictions"""
        valid_targets = []
        
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))
        
        if not valid_targets:
            self.target = None
            return
        
        # Target closest to end of path
        self.target = max(valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
        
        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)
    
    def shoot(self, projectiles):
        """Fire explosive rocket"""
        if self.target:
            projectile = ExplosiveRocket(
                self.x, self.y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.splash_radius, self.splash_damage
            )
            projectile.source_tower_id = self.tower_id
            projectiles.append(projectile)
            
            # Generate currency immediately when firing
            self.generate_firing_currency()
    
    def draw(self, screen, selected: bool = False):
        """Draw explosive tower with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)
        
        if selected:
            pygame.draw.circle(screen, (200, 200, 200), (int(self.x), int(self.y)), int(self.range), 1)
        
        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)
                
                # Draw upgrade indicator if available
                self.draw_upgrade_indicator(screen)
                return
        
        # Fallback to custom drawing
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (0, 0, 0), (int(self.x), int(self.y)), self.size, 2)
        
        # Draw larger, more powerful missile launcher
        launcher_rect = pygame.Rect(self.x - 8, self.y - 12, 16, 10)
        pygame.draw.rect(screen, (100, 100, 100), launcher_rect)
        pygame.draw.rect(screen, (60, 60, 60), launcher_rect, 2)  # Dark border
        
        # Draw missiles - larger for more powerful tower
        for i in range(3):  # More missiles
            missile_x = self.x - 6 + i * 6
            missile_y = self.y - 10
            pygame.draw.circle(screen, (255, 0, 0), (int(missile_x), int(missile_y)), 3)  # Larger missiles
            pygame.draw.circle(screen, (255, 255, 0), (int(missile_x), int(missile_y)), 1)  # Gold tip
        
        # Draw upgrade indicator if available
        self.draw_upgrade_indicator(screen)



    def acquire_target_optimized(self, enemies):
        """Optimized targeting with restrictions"""
        if not enemies:
            self.target = None
            return
        
        range_squared = self.range * self.range
        valid_targets = []
        
        for enemy in enemies:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy
            
            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                valid_targets.append((enemy, actual_distance))
                if len(valid_targets) >= 10:
                    break
        
        if not valid_targets:
            self.target = None
            return
        
        # Target closest to end of path (default strategy)
        self.target = max(valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
        
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)
    
    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)
        
        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate
        
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier

class ExplosiveRocket(Projectile):
    """Explosive rocket projectile with massive AOE damage"""
    
    def __init__(self, x, y, target_x, target_y, speed, damage, splash_radius, splash_damage):
        super().__init__(x, y, target_x, target_y, speed, damage, 'explosive')
        self.splash_radius = splash_radius
        self.splash_damage = splash_damage
        self.color = (255, 165, 0)  # Orange
        self.size = 7  # Bigger rocket
        
        self.active = True
        self.should_remove = False
        self.trail_positions = []
        
    def update(self):
        """Update projectile position"""
        super().update()
        
        # Add to trail
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 6:
            self.trail_positions.pop(0)
        
        # Remove if off screen
        if self.x < 0 or self.x > 1200 or self.y < 0 or self.y > 800:
            self.active = False
            self.should_remove = True
    
    def draw(self, screen):
        """Draw rocket with trail"""
        if self.active:
            # Draw trail
            for i, (trail_x, trail_y) in enumerate(self.trail_positions):
                alpha = (i + 1) / len(self.trail_positions)
                trail_color = (int(255 * alpha), int(165 * alpha), 0)
                pygame.draw.circle(screen, trail_color, (int(trail_x), int(trail_y)), max(1, int(4 * alpha)))
            
            # Draw larger, more powerful rocket
            pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
            pygame.draw.circle(screen, (255, 0, 0), (int(self.x), int(self.y)), 4)  # Bigger core
            pygame.draw.circle(screen, (255, 255, 0), (int(self.x), int(self.y)), 2)  # Gold center
    
    def check_collision(self, enemies):
        """Check collision with enemies and create explosion"""
        if not self.active:
            return {'hit': False, 'damage': 0, 'tower_id': None}
            
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= 15:  # Larger collision detection for bigger explosions
                total_damage = 0
                # Damage all enemies in splash radius
                for enemy_in_range in enemies:
                    explosion_distance = math.sqrt((enemy_in_range.x - self.x)**2 + (enemy_in_range.y - self.y)**2)
                    if explosion_distance <= self.splash_radius:
                        if explosion_distance < 30:  # Larger direct hit radius
                            damage_dealt = enemy_in_range.take_damage(self.damage, 'explosive')
                        else:  # Splash damage
                            damage_dealt = enemy_in_range.take_damage(self.splash_damage, 'explosive')
                        total_damage += damage_dealt
                
                self.active = False
                self.should_remove = True
                return {'hit': True, 'damage': total_damage, 'tower_id': getattr(self, 'source_tower_id', None)}
        
        return {'hit': False, 'damage': 0, 'tower_id': None}
                
    def explode(self, enemies):
        """Create massive explosion and damage all nearby enemies"""
        # Damage all enemies in splash radius
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.splash_radius:
                # Direct hit gets full damage, splash gets reduced damage
                if distance < 20:  # Direct hit
                    enemy.take_damage(self.damage)
                else:  # Splash damage
                    enemy.take_damage(self.splash_damage)
        
        self.active = False
        self.should_remove = True
