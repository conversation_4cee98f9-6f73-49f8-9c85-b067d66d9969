import pygame
import math
import uuid
from typing import List, Optional, Dict
from config.game_config import get_balance_config
from game_systems.tower_upgrade_system import UpgradeType

class Tower:
    """Base class for all towers"""
    def __init__(self, x: int, y: int, tower_type: str = 'basic'):
        self.x = x
        self.y = y
        self.tower_type = tower_type
        
        # Unique identifier for currency tracking
        self.tower_id = str(uuid.uuid4())
        
        # Grid position (will be set by tower manager)
        self.grid_x = 0
        self.grid_y = 0
        
        # Base stats - to be overridden by subclasses
        self.base_range = 100
        self.base_damage = 1
        self.base_fire_rate = 60  # frames between shots
        self.projectile_speed = 5
        self.size = 15
        self.color = (0, 255, 0)  # Green by default
        
        # Current stats (base + upgrades + terrain effects)
        self.range = self.base_range
        self.damage = self.base_damage
        self.fire_rate = self.base_fire_rate
        
        # Terrain effects tracking
        self.terrain_effects_applied = False
        self.terrain_type = None
        
        # Flag to track if subclass has finished initialization
        self._initialization_complete = False
        
        # Upgrade tracking
        self.upgrades: Dict[UpgradeType, int] = {
            UpgradeType.DAMAGE: 0,
            UpgradeType.RANGE: 0,
            UpgradeType.UTILITY: 0
        }
        
        # State
        self.fire_timer = 0
        self.target: Optional[object] = None
        self.angle = 0
        
        # Currency tracking
        self.total_damage_dealt = 0
        
        # Reference to game systems (set by tower manager)
        self.map_reference = None
        self.upgrade_system_reference = None
        
    def set_grid_position(self, grid_x: int, grid_y: int):
        """Set the grid position and apply terrain effects"""
        self.grid_x = grid_x
        self.grid_y = grid_y
        # Reset terrain effects and reapply for new position
        if self.map_reference and self._initialization_complete:
            self.terrain_effects_applied = False
            self.apply_terrain_effects()
    
    def set_map_reference(self, map_obj):
        """Set reference to map for terrain effects"""
        self.map_reference = map_obj
        if not self.terrain_effects_applied and self._initialization_complete:
            self.apply_terrain_effects()
    
    def finalize_initialization(self):
        """Call this after subclass sets its stats to update base values"""
        # Prevent multiple initializations
        if self._initialization_complete:
            return
            
        self.base_range = self.range
        self.base_damage = self.damage
        self.base_fire_rate = self.fire_rate
        self._initialization_complete = True
        
        # Apply terrain effects if map reference is already set
        if self.map_reference and not self.terrain_effects_applied:
            self.apply_terrain_effects()
    
    def set_upgrade_system_reference(self, upgrade_system):
        """Set reference to upgrade system for currency generation"""
        self.upgrade_system_reference = upgrade_system
    
    def apply_terrain_effects(self):
        """Apply terrain-specific effects to this tower"""
        if not self.map_reference or self.terrain_effects_applied:
            return
            
        from game_systems.terrain_types import get_terrain_property
        
        terrain_type = self.map_reference.get_terrain_at_grid(self.grid_x, self.grid_y)
        self.terrain_type = terrain_type
        special_rules = get_terrain_property(terrain_type, 'special_rules')
        
        if special_rules == 'reduced_range':
            # Forest terrain: reduce range by 20% but increase damage by 30%
            # Use current stats (base + upgrades) instead of just base stats
            current_range = self.range
            current_damage = self.damage
            self.range = int(current_range * 0.8)
            self.damage = int(current_damage * 1.3)
        elif special_rules == 'water_only':
            # Water terrain gives special bonuses to certain towers
            if hasattr(self, 'freeze_duration'):
                self.freeze_duration = int(self.freeze_duration * 1.5)
            # Water also increases range by 10% for water-compatible towers
            current_range = self.range
            self.range = int(current_range * 1.1)
        
        self.terrain_effects_applied = True
    
    def track_damage_and_generate_currency(self, damage_dealt: int):
        """Track damage and generate currency - centralized for all towers using config values"""
        if damage_dealt > 0:
            self.add_damage_dealt(damage_dealt)
            
            # Generate currency using config value
            config = get_balance_config()
            currency_amount = max(1, damage_dealt // config['currency']['damage_divisor'])
            
            if self.upgrade_system_reference:
                self.upgrade_system_reference.add_tower_currency(
                    self.tower_id, self.tower_type, currency_amount
                )
    
    def track_utility_hit(self):
        """Track utility hit for support towers - centralized using config values"""
        # Support towers get minimal currency for successful hits
        config = get_balance_config()
        currency_amount = config['currency']['utility_hit_reward']
        
        if self.upgrade_system_reference:
            self.upgrade_system_reference.add_tower_currency(
                self.tower_id, self.tower_type, currency_amount
            )

    def update(self, enemies, projectiles):
        """Update tower behavior - acquire targets and shoot if ready"""
        self.acquire_target(enemies)
        
        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate
        
        if self.fire_timer > 0:
            self.fire_timer -= 1
    
    def update_with_speed(self, enemies, projectiles, speed_multiplier: float):
        """Update tower with speed multiplier for performance optimization"""
        self.acquire_target(enemies)
        
        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate
        
        # Decrease fire timer based on speed multiplier
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier
    
    def update_optimized(self, enemies, projectiles):
        """Update tower behavior with performance optimizations"""
        self.acquire_target_optimized(enemies)
        
        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate
        
        if self.fire_timer > 0:
            self.fire_timer -= 1
    
    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update tower with speed multiplier and performance optimizations"""
        self.acquire_target_optimized(enemies)
        
        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate
        
        # Decrease fire timer based on speed multiplier
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier
    
    def acquire_target(self, enemies: List):
        """Find the best target based on tower's targeting strategy"""
        valid_targets = []
        
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))
        
        if not valid_targets:
            self.target = None
            return
        
        # Default targeting: closest to end of path
        self.target = max(valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
        
        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)
    
    def acquire_target_optimized(self, enemies: List):
        """Optimized targeting using squared distance to avoid sqrt operations"""
        if not enemies:
            self.target = None
            return
        
        range_squared = self.range * self.range
        valid_targets = []
        
        # Use squared distance for initial filtering (avoids sqrt)
        for enemy in enemies:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy
            
            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                # Only calculate actual distance for valid targets
                actual_distance = math.sqrt(distance_squared)
                valid_targets.append((enemy, actual_distance))
                
                # Early termination: if we have enough targets, we can stop
                # This helps when there are many enemies but we only need a few candidates
                if len(valid_targets) >= 10:  # Reasonable limit for targeting
                    break
        
        if not valid_targets:
            self.target = None
            return
        
        # Default targeting: closest to end of path
        self.target = max(valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
        
        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)
    
    def can_target_enemy(self, enemy) -> bool:
        """Check if this tower can target the given enemy (base implementation)"""
        # Check if enemy is immune to this tower type
        if enemy.is_immune_to_tower(self.tower_type):
            return False
        return True
    
    def generate_firing_currency(self):
        """Generate currency immediately when tower fires a projectile"""
        config = get_balance_config()
        currency_amount = config['currency']['firing_reward']
        
        if self.upgrade_system_reference:
            self.upgrade_system_reference.add_tower_currency(
                self.tower_id, self.tower_type, currency_amount
            )

    def shoot(self, projectiles: List):
        """Create and fire a projectile at the target"""
        if self.target:
            from projectiles import BasicProjectile
            projectile = BasicProjectile(
                self.x, self.y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.tower_type
            )
            # Link projectile to tower for damage tracking
            projectile.source_tower_id = self.tower_id
            projectiles.append(projectile)
            
            # Generate currency immediately when firing
            self.generate_firing_currency()
    
    def add_damage_dealt(self, damage: int):
        """Track damage dealt by this tower for currency generation"""
        # Only count positive damage (actual damage), ignore healing (negative values)
        if damage > 0:
            self.total_damage_dealt += damage
    
    def reset_stats_to_base(self):
        """Reset stats to base values before applying upgrades and terrain effects"""
        self.range = self.base_range
        self.damage = self.base_damage
        self.fire_rate = self.base_fire_rate
        
        # Reset terrain effects flag but don't apply yet
        # Terrain effects will be applied after upgrades to avoid conflicts
        self.terrain_effects_applied = False
    
    def get_upgrade_level(self, upgrade_type: UpgradeType) -> int:
        """Get the current upgrade level for a specific upgrade type"""
        return self.upgrades.get(upgrade_type, 0)
    
    def set_upgrade_level(self, upgrade_type: UpgradeType, level: int):
        """Set the upgrade level for a specific upgrade type"""
        self.upgrades[upgrade_type] = level
    
    def has_upgrade_available(self) -> bool:
        """Check if this tower has any upgrades available"""
        if not self.upgrade_system_reference:
            return False
        
        from game_systems.tower_upgrade_system import UpgradeType
        
        # Check if any upgrade type is available
        for upgrade_type in UpgradeType:
            current_level = self.get_upgrade_level(upgrade_type)
            if self.upgrade_system_reference.can_upgrade(self.tower_id, self.tower_type, upgrade_type, current_level):
                return True
        
        return False
    
    def draw_upgrade_indicator(self, screen):
        """Draw upgrade available indicator - can be called by custom draw methods"""
        if self.has_upgrade_available():
            # Draw a small upgrade icon in the top-right corner
            upgrade_x = int(self.x + self.size + 2)
            upgrade_y = int(self.y - self.size - 2)
            
            # Draw gold coin background
            pygame.draw.circle(screen, (255, 215, 0), (upgrade_x, upgrade_y), 6)  # Gold coin
            pygame.draw.circle(screen, (0, 0, 0), (upgrade_x, upgrade_y), 6, 1)   # Black border
            
            # Draw "↑" symbol for upgrade
            font = pygame.font.Font(None, 14)
            text = font.render("↑", True, (0, 0, 0))
            text_rect = text.get_rect(center=(upgrade_x, upgrade_y))
            screen.blit(text, text_rect)
    
    def apply_damage_boost(self, multiplier: float):
        """Apply damage boost from rock removal rewards"""
        self.damage = int(self.damage * multiplier)
    
    def remove_damage_boost(self):
        """Remove damage boost and restore normal damage"""
        # Recalculate damage from base + upgrades + terrain effects
        self.damage = self.base_damage
        
        # Reapply upgrades
        for upgrade_type, level in self.upgrades.items():
            if upgrade_type == UpgradeType.DAMAGE and level > 0:
                # Apply damage upgrades
                upgrade_bonus = level * 2  # Assuming +2 damage per level
                self.damage += upgrade_bonus
        
        # Reapply terrain effects
        if self.terrain_effects_applied and self.terrain_type:
            from game_systems.terrain_types import get_terrain_property
            special_rules = get_terrain_property(self.terrain_type, 'special_rules')
            
            if special_rules == 'reduced_range':
                # Forest terrain: increase damage by 30%
                self.damage = int(self.damage * 1.3)
    
    def draw(self, screen: pygame.Surface, selected: bool = False):
        """Draw the tower on the screen with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)
        
        # Draw range circle only when selected
        if selected:
            pygame.draw.circle(screen, (200, 200, 200), (self.x, self.y), self.range, 1)
        
        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)
                
                # Draw upgrade available indicator
                self.draw_upgrade_indicator(screen)
                return
        
        # Fallback to original geometric drawing
        # Draw tower base
        pygame.draw.circle(screen, self.color, (self.x, self.y), self.size)
        pygame.draw.circle(screen, (0, 0, 0), (self.x, self.y), self.size, 2)
        
        # Draw barrel pointing at target
        if self.target:
            barrel_length = self.size + 5
            end_x = self.x + math.cos(self.angle) * barrel_length
            end_y = self.y + math.sin(self.angle) * barrel_length
            pygame.draw.line(screen, (0, 0, 0), (self.x, self.y), (end_x, end_y), 3)
        
        # Draw upgrade available indicator
        self.draw_upgrade_indicator(screen)
    
    def set_sprite_manager(self, sprite_manager):
        """Set the sprite manager for this tower"""
        self._sprite_manager = sprite_manager