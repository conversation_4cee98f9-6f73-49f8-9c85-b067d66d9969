"""
Tower Upgrade System - Manages tower upgrades with three paths and tower-specific currencies
"""
from typing import Dict, List, Tuple, Optional
from enum import Enum

class UpgradeType(Enum):
    DAMAGE = "damage"
    RANGE = "range" 
    UTILITY = "utility"

class TowerUpgradeSystem:
    """Manages tower upgrades with three simultaneous upgrade paths"""
    
    def __init__(self):
        # Tower-specific currencies (gained by using towers)
        self.tower_currencies = {}
        
        # Tower-specific unique upgrade definitions
        self.upgrade_definitions = {
            'basic': {
                UpgradeType.DAMAGE: {
                    'name': 'Munitions',
                    'description': 'Improved ammunition',
                    'max_level': 5,
                    'base_cost': 25,
                    'effects': [
                        {'damage': 2},   # Level 1: +2 damage (buffed from +1)
                        {'damage': 3},   # Level 2: +3 more damage (buffed from +1)
                        {'damage': 4},   # Level 3: +4 more damage (buffed from +2)
                        {'damage': 5},   # Level 4: +5 more damage (buffed from +2)
                        {'damage': 6}    # Level 5: +6 more damage (buffed from +3)
                    ]
                },
                UpgradeType.RANGE: {
                    'name': 'Scope',
                    'description': 'Better targeting systems',
                    'max_level': 5,
                    'base_cost': 20,
                    'effects': [
                        {'range': 15},   # Level 1: +15 range (buffed from +10)
                        {'range': 12},   # Level 2: +12 more range (buffed from +8)
                        {'range': 12},   # Level 3: +12 more range (buffed from +8)
                        {'range': 15},   # Level 4: +15 more range (buffed from +10)
                        {'range': 18}    # Level 5: +18 more range (buffed from +12)
                    ]
                },
                UpgradeType.UTILITY: {
                    'name': 'Rapid Fire',
                    'description': 'Faster attack speed',
                    'max_level': 5,
                    'base_cost': 30,
                    'effects': [
                        {'fire_rate': -5},  # Level 1: 5 frames faster (buffed from -3)
                        {'fire_rate': -4},  # Level 2: 4 more frames faster (buffed from -2)
                        {'fire_rate': -4},  # Level 3: 4 more frames faster (buffed from -2)
                        {'fire_rate': -5},  # Level 4: 5 more frames faster (buffed from -3)
                        {'fire_rate': -8}   # Level 5: 8 more frames faster (buffed from -5)
                    ]
                }
            },
            'sniper': {
                UpgradeType.DAMAGE: {
                    'name': 'High Caliber',
                    'description': 'Armor-piercing rounds',
                    'max_level': 5,
                    'base_cost': 40,
                    'effects': [
                        {'damage': 8}, {'damage': 12}, {'damage': 18}, {'damage': 25}, {'damage': 35}  # Buffed significantly
                    ]
                },
                UpgradeType.RANGE: {
                    'name': 'Long Range Optics',  
                    'description': 'Extended vision range',
                    'max_level': 5,
                    'base_cost': 35,
                    'effects': [
                        {'range': 35}, {'range': 45}, {'range': 60}, {'range': 75}, {'range': 110}  # Buffed significantly
                    ]
                },
                UpgradeType.UTILITY: {
                    'name': 'Tactical Systems',
                    'description': 'Advanced targeting capabilities',
                    'max_level': 4,
                    'base_cost': 50,
                    'effects': [
                        {'can_target_invisible': True},  # Level 1: See invisible
                        {'crit_chance': 0.25},          # Level 2: 25% crit chance (buffed from 15%)
                        {'crit_multiplier': 3.5},       # Level 3: 3.5x crit damage (buffed from 2.5x)
                        {'armor_penetration': 0.5}      # Level 4: 50% armor penetration (buffed from 30%)
                    ]
                }
            }
        }
        
        # Initialize upgrade definitions for all tower types
        self._initialize_all_upgrades()
    
    def _initialize_all_upgrades(self):
        """Initialize unique upgrade definitions for all tower types"""
        
        # Freezer Tower - Single target freeze specialist
        self.upgrade_definitions['freezer'] = {
            UpgradeType.DAMAGE: {
                'name': 'Frost Bite',
                'description': 'Enhanced freeze damage',
                'max_level': 4,
                'base_cost': 30,
                'effects': [
                    {'damage': 4}, {'damage': 6}, {'damage': 8}, {'damage': 12}  # Single target damage focus
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Arctic Reach',
                'description': 'Extends freezing range',
                'max_level': 5,
                'base_cost': 25,
                'effects': [
                    {'range': 22}, {'range': 26}, {'range': 32}, {'range': 40}, {'range': 50}  # Buffed from 15,18,22,28,35
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Deep Freeze',
                'description': 'Longer and more potent freeze effects',
                'max_level': 4,
                'base_cost': 40,
                'effects': [
                    {'freeze_duration': 30}, {'freeze_duration': 45}, {'freeze_duration': 60}, {'complete_freeze': True}  # Extended freeze duration
                ]
            }
        }
        
        # Detector Tower - Surveillance specialist
        self.upgrade_definitions['detector'] = {
            UpgradeType.DAMAGE: {
                'name': 'Pulse Blast',
                'description': 'Damaging detection pulses',
                'max_level': 3,
                'base_cost': 35,
                'effects': [
                    {'damage': 6}, {'pulse_damage': 4}, {'pulse_stun': 0.8}  # Buffed from 3,2,0.5
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Long Range Radar',
                'description': 'Massive detection range',
                'max_level': 4,
                'base_cost': 20,
                'effects': [
                    {'detection_range': 60}, {'detection_range': 90}, {'detection_range': 120}, {'detection_range': 180}  # Buffed significantly
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Advanced Scanner',
                'description': 'Detects more enemies and reveals weaknesses',
                'max_level': 4,
                'base_cost': 45,
                'effects': [
                    {'max_detections': 3}, {'max_detections': 5}, {'weakness_reveal': True}, {'team_vision_boost': 0.25}  # Buffed detection count and team boost
                ]
            }
        }
        
        # Continue with remaining towers
        self._add_remaining_towers()
    
    def _add_remaining_towers(self):
        """Add unique upgrade definitions for remaining tower types"""
        
        # Anti-Air Tower - Anti-aircraft specialist
        self.upgrade_definitions['antiair'] = {
            UpgradeType.DAMAGE: {
                'name': 'Heat Seeking',
                'description': 'Guided missiles with explosive power',
                'max_level': 5,
                'base_cost': 45,
                'effects': [
                    {'damage': 10},  {'damage': 15},  {'damage': 20}, {'damage': 28}, {'damage': 40}  # Buffed from 6,8,12,15,20
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Sky Radar',
                'description': 'Extended air defense coverage',
                'max_level': 5,
                'base_cost': 35,
                'effects': [
                    {'range': 30}, {'range': 40}, {'range': 55}, {'range': 70}, {'range': 90}  # Buffed from 20,25,35,45,60
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Multi-Lock',
                'description': 'Advanced targeting systems',
                'max_level': 4,
                'base_cost': 50,
                'effects': [
                    {'can_target_invisible': True}, # Level 1: detect stealth aircraft
                    {'multi_target': 3},           # Level 2: target 3 enemies at once (buffed from 2)
                    {'homing_strength': 0.5},      # Level 3: stronger homing (buffed from 0.3)
                    {'flak_burst': True}           # Level 4: explosive flak bursts
                ]
            }
        }
        
        # Cannon Tower - Artillery specialist (ENHANCED for new base stats)
        self.upgrade_definitions['cannon'] = {
            UpgradeType.DAMAGE: {
                'name': 'Heavy Artillery',
                'description': 'Devastating explosive shells',
                'max_level': 4,
                'base_cost': 55,
                'effects': [
                    {'damage': 35}, {'damage': 50}, {'splash_damage': 25}, {'splash_damage': 40}  # Buffed from 20,30,15,25
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Long Range Barrel',
                'description': 'Extended siege range',
                'max_level': 4,
                'base_cost': 40,
                'effects': [
                    {'range': 60}, {'range': 90}, {'range': 120}, {'range': 180}  # Buffed from 40,60,80,120
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Siege Mode',
                'description': 'Area denial capabilities',
                'max_level': 4,
                'base_cost': 60,
                'effects': [
                    {'splash_radius': 22}, {'fire_rate': -12}, {'ground_crater': True}, {'armor_shred': 0.5}  # Buffed splash radius and armor shred
                ]
            }
        }
        
        # Continue with final towers
        self._add_final_towers()
    
    def _add_final_towers(self):
        """Add unique upgrade definitions for final tower types"""
        
        # Laser Tower - Energy weapons specialist
        self.upgrade_definitions['laser'] = {
            UpgradeType.DAMAGE: {
                'name': 'Photon Amplifier',
                'description': 'Concentrated energy beams',
                'max_level': 5,
                'base_cost': 50,
                'effects': [
                    {'damage': 7}, {'damage': 10}, {'damage': 15}, {'damage': 20}, {'damage': 30}  # Buffed from 4,6,9,12,18
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Beam Focus',
                'description': 'Extended laser range',
                'max_level': 4,
                'base_cost': 40,
                'effects': [
                    {'range': 30}, {'range': 45}, {'range': 65}, {'range': 95}  # Buffed from 20,30,45,65
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Pulse Mode',
                'description': 'Advanced laser capabilities',
                'max_level': 4,
                'base_cost': 60,
                'effects': [
                    {'fire_rate': -12}, {'beam_piercing': 4}, {'charge_burst': True}, {'energy_overload': 0.75}  # Buffed significantly
                ]
            }
        }
        
        # Lightning Tower - Electric storm specialist
        self.upgrade_definitions['lightning'] = {
            UpgradeType.DAMAGE: {
                'name': 'High Voltage',
                'description': 'Devastating electrical discharge',
                'max_level': 5,
                'base_cost': 45,
                'effects': [
                    {'damage': 5}, {'damage': 7}, {'damage': 10}, {'damage': 14}, {'damage': 20}  # Buffed from 3,4,6,8,12
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Tesla Coil',
                'description': 'Extended electrical reach',
                'max_level': 4,
                'base_cost': 35,
                'effects': [
                    {'range': 22}, {'range': 30}, {'range': 45}, {'range': 65}  # Buffed from 15,20,30,45
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Chain Storm',
                'description': 'Electrical chain reactions',
                'max_level': 4,
                'base_cost': 55,
                'effects': [
                    {'chain_count': 3}, {'chain_range': 40}, {'stun_chance': 0.35}, {'storm_field': True}  # Buffed chain count and effects
                ]
            }
        }
        
        # Flame Tower - Pyromancer specialist
        self.upgrade_definitions['flame'] = {
            UpgradeType.DAMAGE: {
                'name': 'Inferno Blast',
                'description': 'Intense burning flames',
                'max_level': 4,
                'base_cost': 40,
                'effects': [
                    {'damage': 5}, {'damage': 8}, {'burn_damage': 4}, {'burn_damage': 6}  # Buffed from 3,5,2,3
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Flame Reach',
                'description': 'Extended flame projection',
                'max_level': 4,
                'base_cost': 30,
                'effects': [
                    {'range': 18}, {'range': 26}, {'range': 36}, {'range': 50}  # Buffed from 12,18,25,35
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Wildfire',
                'description': 'Spreading flame effects',
                'max_level': 4,
                'base_cost': 50,
                'effects': [
                    {'burn_duration': 60}, {'flame_spread': True}, {'fire_rate': -10}, {'molten_ground': True}  # Buffed burn duration and fire rate
                ]
            }
        }
        
        # Add remaining specialized towers
        self._add_specialized_towers()
    
    def _add_specialized_towers(self):
        """Add unique upgrades for remaining specialized tower types"""
        
        # Ice Tower - Cryogenic specialist
        self.upgrade_definitions['ice'] = {
            UpgradeType.DAMAGE: {
                'name': 'Absolute Zero',
                'description': 'Extreme cold damage',
                'max_level': 4,
                'base_cost': 35,
                'effects': [
                    {'damage': 4}, {'damage': 7}, {'freeze_damage': 5}, {'shatter_damage': 15}  # Buffed from 2,4,3,8
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Blizzard Field',
                'description': 'Widespread ice coverage',
                'max_level': 4,
                'base_cost': 30,
                'effects': [
                    {'range': 26}, {'range': 36}, {'range': 50}, {'range': 70}  # Buffed from 18,25,35,50
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Permafrost',
                'description': 'Permanent ice effects',
                'max_level': 4,
                'base_cost': 45,
                'effects': [
                    {'freeze_duration': 90}, {'ice_armor': -0.3}, {'permafrost_field': True}, {'brittle_effect': 0.5}  # Buffed duration and effects
                ]
            }
        }
        
        # Poison Tower - Biochemical specialist
        self.upgrade_definitions['poison'] = {
            UpgradeType.DAMAGE: {
                'name': 'Toxin Dose',
                'description': 'Lethal poison compounds',
                'max_level': 5,
                'base_cost': 30,
                'effects': [
                    {'damage': 2}, {'poison_damage': 4}, {'poison_damage': 6}, {'poison_damage': 8}, {'poison_damage': 12}  # Buffed significantly
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Gas Cloud',
                'description': 'Poison gas dispersion',
                'max_level': 4,
                'base_cost': 25,
                'effects': [
                    {'range': 22}, {'range': 32}, {'range': 44}, {'range': 60}  # Buffed from 15,22,30,40
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Plague Spread',
                'description': 'Contagious poison effects',
                'max_level': 4,
                'base_cost': 40,
                'effects': [
                    {'poison_duration': 180}, {'plague_spread': True}, {'armor_corrosion': 0.25}, {'death_cloud': True}  # Buffed duration and corrosion
                ]
            }
        }
        
        # Explosive Tower - Demolition specialist (ENHANCED for new base stats)
        self.upgrade_definitions['explosive'] = {
            UpgradeType.DAMAGE: {
                'name': 'High Explosives',
                'description': 'Maximum destruction',
                'max_level': 4,
                'base_cost': 60,
                'effects': [
                    {'damage': 40}, {'damage': 60}, {'splash_damage': 35}, {'splash_damage': 50}  # Buffed from 25,35,20,30
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Artillery Range',
                'description': 'Long range bombardment',
                'max_level': 4,
                'base_cost': 45,
                'effects': [
                    {'range': 75}, {'range': 110}, {'range': 150}, {'range': 220}  # Buffed from 50,75,100,150
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Cluster Bombs',
                'description': 'Multiple warheads',
                'max_level': 4,
                'base_cost': 70,
                'effects': [
                    {'splash_radius': 35}, {'cluster_count': 5}, {'delayed_explosions': True}, {'bunker_buster': True}  # Buffed splash and cluster count
                ]
            }
        }
        
        # Missile Tower - Guided munitions specialist
        self.upgrade_definitions['missile'] = {
            UpgradeType.DAMAGE: {
                'name': 'Warhead Power',
                'description': 'High-yield warheads',
                'max_level': 5,
                'base_cost': 50,
                'effects': [
                    {'damage': 12}, {'damage': 20}, {'damage': 30}, {'damage': 42}, {'damage': 60}  # Buffed from 8,12,18,25,35
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Long Range Strike',
                'description': 'Intercontinental range',
                'max_level': 4,
                'base_cost': 40,
                'effects': [
                    {'range': 50}, {'range': 75}, {'range': 110}, {'range': 180}  # Buffed from 35,50,75,120
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Smart Missiles',
                'description': 'Advanced guidance systems',
                'max_level': 4,
                'base_cost': 55,
                'effects': [
                    {'homing_accuracy': 0.95}, {'multi_warhead': 4}, {'emp_effect': True}, {'orbital_strike': True}  # Buffed accuracy and warhead count
                ]
            }
        }
        
        # Splash Tower - Area effect specialist
        self.upgrade_definitions['splash'] = {
            UpgradeType.DAMAGE: {
                'name': 'Shockwave',
                'description': 'Devastating area damage',
                'max_level': 4,
                'base_cost': 35,
                'effects': [
                    {'damage': 7}, {'splash_damage': 10}, {'splash_damage': 15}, {'splash_damage': 22}  # Buffed from 4,6,9,12
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Wide Area',
                'description': 'Extended splash coverage',
                'max_level': 4,
                'base_cost': 30,
                'effects': [
                    {'range': 30}, {'splash_radius': 16}, {'splash_radius': 24}, {'splash_radius': 36}  # Buffed significantly
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Chain Reaction',
                'description': 'Cascading explosions',
                'max_level': 4,
                'base_cost': 45,
                'effects': [
                    {'fire_rate': -7}, {'chain_splash': 4}, {'knockback_force': 0.5}, {'seismic_wave': True}  # Buffed fire rate and chain count
                ]
            }
        }
        
        # Destroyer Tower - Naval artillery specialist
        self.upgrade_definitions['destroyer'] = {
            UpgradeType.DAMAGE: {
                'name': 'Naval Artillery',
                'description': 'Enhanced naval missile damage',
                'max_level': 5,
                'base_cost': 80,
                'effects': [
                    {'damage': 20}, {'damage': 30}, {'damage': 45}, {'damage': 65}, {'damage': 90}  # Massive naval firepower
                ]
            },
            UpgradeType.RANGE: {
                'name': 'Long Range Naval',
                'description': 'Extended naval engagement range',
                'max_level': 4,
                'base_cost': 60,
                'effects': [
                    {'range': 75}, {'range': 120}, {'range': 180}, {'range': 280}  # Extreme naval range
                ]
            },
            UpgradeType.UTILITY: {
                'name': 'Advanced Naval Systems',
                'description': 'Sophisticated naval warfare capabilities',
                'max_level': 4,
                'base_cost': 100,
                'effects': [
                    {'explosion_radius': 15, 'explosion_damage': 8},  # Level 1: Enhanced explosion
                    {'explosion_radius': 20, 'charge_duration': -3},  # Level 2: Faster charging
                    {'explosion_damage': 12, 'charge_duration': -4},  # Level 3: More damage, faster
                    {'naval_superiority': True}  # Level 4: Special naval ability
                ]
            }
        }
    


    def get_tower_currency(self, tower_id: str, tower_type: str) -> int:
        """Get the current currency for a specific tower"""
        key = f"{tower_id}_{tower_type}"
        return self.tower_currencies.get(key, 0)
    
    def add_tower_currency(self, tower_id: str, tower_type: str, amount: int):
        """Add currency for a specific tower"""
        key = f"{tower_id}_{tower_type}"
        current = self.tower_currencies.get(key, 0)
        self.tower_currencies[key] = current + amount
    
    def get_upgrade_cost(self, tower_type: str, upgrade_type: UpgradeType, current_level: int) -> dict:
        """Calculate the cost for the next upgrade level - Returns both currency and money costs"""
        if tower_type not in self.upgrade_definitions:
            return {'currency_cost': 999999, 'money_cost': 999999}  # Invalid tower type
        
        upgrade_def = self.upgrade_definitions[tower_type][upgrade_type]
        if current_level >= upgrade_def['max_level']:
            return {'currency_cost': 999999, 'money_cost': 999999}  # Max level reached
        
        base_cost = upgrade_def['base_cost']
        # Exponential cost scaling: cost increases by 50% each level
        currency_cost = int(base_cost * (1.5 ** current_level))
        
        # Money cost is 25% of currency cost (scaled appropriately)
        money_cost = max(10, int(currency_cost * 0.25))
        
        return {'currency_cost': currency_cost, 'money_cost': money_cost}
    
    def can_upgrade(self, tower_id: str, tower_type: str, upgrade_type: UpgradeType, current_level: int, player_money: int = 0) -> bool:
        """Check if a tower can be upgraded with both currency and money"""
        costs = self.get_upgrade_cost(tower_type, upgrade_type, current_level)
        currency = self.get_tower_currency(tower_id, tower_type)
        
        currency_sufficient = currency >= costs['currency_cost']
        money_sufficient = player_money >= costs['money_cost']
        costs_valid = costs['currency_cost'] < 999999 and costs['money_cost'] < 999999
        
        return currency_sufficient and money_sufficient and costs_valid
    
    def upgrade_tower(self, tower_id: str, tower_type: str, upgrade_type: UpgradeType, current_level: int, player_money: int = 0) -> dict:
        """Attempt to upgrade a tower - Returns result with costs deducted"""
        if self.can_upgrade(tower_id, tower_type, upgrade_type, current_level, player_money):
            costs = self.get_upgrade_cost(tower_type, upgrade_type, current_level)
            key = f"{tower_id}_{tower_type}"
            
            # Deduct tower currency
            self.tower_currencies[key] = self.tower_currencies.get(key, 0) - costs['currency_cost']
            
            # Return success with money cost to be deducted by caller
            return {
                'success': True, 
                'money_cost': costs['money_cost'], 
                'currency_cost': costs['currency_cost']
            }
        
        return {'success': False, 'money_cost': 0, 'currency_cost': 0}
    
    def get_upgrade_info(self, tower_type: str, upgrade_type: UpgradeType) -> Dict:
        """Get information about an upgrade path"""
        if tower_type in self.upgrade_definitions:
            return self.upgrade_definitions[tower_type][upgrade_type]
        return {}
    
    def apply_upgrades_to_tower(self, tower, tower_id: str):
        """Apply all upgrades to a tower"""
        tower_type = tower.tower_type
        
        # Reset to base stats first
        tower.reset_stats_to_base()
        
        # Apply each upgrade type
        for upgrade_type in UpgradeType:
            level = tower.get_upgrade_level(upgrade_type)
            if level > 0:
                self._apply_upgrade_effects(tower, tower_type, upgrade_type, level)
        
        # Apply terrain effects AFTER upgrades to avoid conflicts
        if not tower.terrain_effects_applied:
            tower.apply_terrain_effects()
    
    def _apply_upgrade_effects(self, tower, tower_type: str, upgrade_type: UpgradeType, level: int):
        """Apply specific upgrade effects to a tower"""
        if tower_type not in self.upgrade_definitions:
            return
        
        upgrade_def = self.upgrade_definitions[tower_type][upgrade_type]
        effects = upgrade_def.get('effects', [])
        
        # Apply effects for each level up to the current level
        for i in range(min(level, len(effects))):
            effect = effects[i]
            for stat, value in effect.items():
                if hasattr(tower, stat):
                    current_value = getattr(tower, stat)
                    if isinstance(value, (int, float)):
                        setattr(tower, stat, current_value + value)
                    else:
                        setattr(tower, stat, value) 