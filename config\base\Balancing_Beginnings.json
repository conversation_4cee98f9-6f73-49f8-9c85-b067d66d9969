{"game_config": {"starting_money": 29, "starting_lives": 17}, "progression_config": {"starting_money": 200, "starting_lives": 17, "economic_scaling": "automatic"}, "tower_config": {"cost_progression": {"early_game_waves": 15, "mid_game_waves": 30, "early_increase_per_wave": 0.016, "mid_increase_per_wave": 0.020999999999999998, "late_increase_per_wave": 0.025, "max_cost_multiplier": 2.4000000000000004}, "dynamic_cost_increase": {"per_tower_built_multiplier": 0.15, "max_per_tower_multiplier": 20}}, "enemy_buffs": {"max_health_modifier": 0.8, "max_speed_modifier": 0.9, "global_damage_modifier": 1.0, "special_abilities_enabled": true}, "wave_config": {"total_waves": 48, "spawn_config": {"base_enemy_count": 11, "base_spawn_delay": 102, "min_spawn_delay": 24.0, "boss_enemy_count": 1}, "round_progression": {"enemy_increase_per_round": {"wave_ranges": {"1-5": 1, "6-10": 2, "11-15": 3, "16-20": 4, "21-25": 5, "26-30": 6, "31-35": 7, "36-40": 8, "41-45": 9, "46-50": 10, "51-55": 11, "56-60": 12, "61-65": 13, "66-70": 14, "71-75": 15, "76-80": 16}, "default": 1}, "spawn_delay_reduction_per_round": {"wave_ranges": {"1-10": 5, "11-20": 8, "21-30": 11, "31-40": 13, "41-50": 16, "51-60": 19, "61-70": 22, "71-80": 24}, "default": 5}, "special_rounds": {"10": {"enemy_multiplier": 2.0, "spawn_delay_multiplier": 0.7000000000000001}, "20": {"enemy_multiplier": 2.5, "spawn_delay_multiplier": 0.6000000000000001}, "30": {"enemy_multiplier": 3.0, "spawn_delay_multiplier": 0.5}, "40": {"enemy_multiplier": 3.5, "spawn_delay_multiplier": 0.4}, "50": {"enemy_multiplier": 4.0, "spawn_delay_multiplier": 0.30000000000000004}, "60": {"enemy_multiplier": 4.5, "spawn_delay_multiplier": 0.2}, "70": {"enemy_multiplier": 5.0, "spawn_delay_multiplier": 0.2}, "80": {"enemy_multiplier": 5.5, "spawn_delay_multiplier": 0.2}}}, "wave_compositions": {"1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.13999999999999999]], "6-10": [["BasicEnemy", 0.35], ["TankEnemy", 0.35], ["FastEnemy", 0.3]], "11-15": [["BasicEnemy", 0.35], ["TankEnemy", 0.35], ["FastEnemy", 0.15], ["FlyingEnemy", 0.15]], "16-20": [["BasicEnemy", 0.2916666666666667], ["TankEnemy", 0.2916666666666667], ["FastEnemy", 0.08333333333333333], ["FlyingEnemy", 0.08333333333333333], ["ShieldedEnemy", 0.08333333333333333], ["ArmoredEnemy", 0.16666666666666669]], "21-30": [["BasicEnemy", 0.19444444444444445], ["TankEnemy", 0.19444444444444445], ["FastEnemy", 0.05555555555555556], ["FlyingEnemy", 0.05555555555555556], ["ShieldedEnemy", 0.05555555555555556], ["ArmoredEnemy", 0.11111111111111113], ["InvisibleEnemy", 0.11111111111111113], ["EnergyShieldEnemy", 0.11111111111111113], ["GroundedEnemy", 0.11111111111111113]], "31-40": [["BasicEnemy", 0.2250000000000001], ["TankEnemy", 0.2250000000000001], ["FastEnemy", 0.0666666666666667], ["FlyingEnemy", 0.0666666666666667], ["ShieldedEnemy", 0.0666666666666667], ["ArmoredEnemy", 0.04375000000000001], ["InvisibleEnemy", 0.04375000000000001], ["EnergyShieldEnemy", 0.04375000000000001], ["GroundedEnemy", 0.04375000000000001], ["RegeneratingEnemy", 0.04375000000000001], ["TeleportingEnemy", 0.04375000000000001], ["FireElementalEnemy", 0.04375000000000001], ["ToxicEnemy", 0.04375000000000001]], "41-50": [["BasicEnemy", 0.22500000000000014], ["TankEnemy", 0.22500000000000014], ["FastEnemy", 0.06666666666666671], ["FlyingEnemy", 0.06666666666666671], ["ShieldedEnemy", 0.06666666666666671], ["ArmoredEnemy", 0.031818181818181836], ["InvisibleEnemy", 0.031818181818181836], ["EnergyShieldEnemy", 0.031818181818181836], ["GroundedEnemy", 0.031818181818181836], ["RegeneratingEnemy", 0.031818181818181836], ["TeleportingEnemy", 0.031818181818181836], ["FireElementalEnemy", 0.031818181818181836], ["ToxicEnemy", 0.031818181818181836], ["SplittingEnemy", 0.031818181818181836], ["PhaseShiftEnemy", 0.031818181818181836], ["BlastProofEnemy", 0.031818181818181836]], "51-60": [["BasicEnemy", 0.22499999999999995], ["TankEnemy", 0.22499999999999995], ["FastEnemy", 0.06666666666666665], ["FlyingEnemy", 0.06666666666666665], ["ShieldedEnemy", 0.06666666666666665], ["ArmoredEnemy", 0.026923076923076914], ["InvisibleEnemy", 0.026923076923076914], ["EnergyShieldEnemy", 0.026923076923076914], ["GroundedEnemy", 0.026923076923076914], ["RegeneratingEnemy", 0.026923076923076914], ["TeleportingEnemy", 0.026923076923076914], ["FireElementalEnemy", 0.026923076923076914], ["ToxicEnemy", 0.026923076923076914], ["SplittingEnemy", 0.026923076923076914], ["PhaseShiftEnemy", 0.026923076923076914], ["BlastProofEnemy", 0.026923076923076914], ["SpectralEnemy", 0.026923076923076914], ["CrystallineEnemy", 0.026923076923076914]], "61-80": [["BasicEnemy", 0.34374999999999994], ["TankEnemy", 0.34374999999999994], ["FastEnemy", 0.10416666666666664], ["FlyingEnemy", 0.10416666666666664], ["ShieldedEnemy", 0.10416666666666664]]}, "boss_waves": {"20": "TimeLordBoss", "25": "ShadowKing", "30": "NecromancerBoss", "35": "CrystalOverlord", "40": "TimeLordBoss", "45": "ShadowKing", "50": "NecromancerBoss", "55": "CrystalOverlord", "60": "TimeLordBoss", "65": "ShadowKing", "70": "NecromancerBoss", "75": "CrystalOverlord", "80": "TimeLordBoss"}, "enemy_scaling": {"health_per_wave": 0.35100000000000003, "speed_per_wave": 0.0585, "reward_per_wave": 0.1296, "size_per_wave": 0.02, "max_health_multiplier": 48.0, "max_speed_multiplier": 4.0, "max_reward_multiplier": 17.6, "max_size_multiplier": 1.8, "damage_scaling_per_wave": 0.09000000000000001, "max_damage_multiplier": 3.2}, "money_config": {"normal_wave_bonus": 65, "boss_wave_bonus": 240}}, "map_config": {"default_map": {"width": 20, "height": 15, "terrain": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 4, 0, 0, 2, 0, 0, 2, 2], [2, 0, 2, 0, 0, 0, 0, 4, 0, 0, 3, 0, 0, 0, 0, 2, 0, 0, 0, 2], [2, 2, 0, 2, 0, 0, 0, 2, 0, 0, 4, 0, 0, 0, 2, 0, 0, 2, 0, 2], [2, 0, 4, 0, 0, 0, 0, 0, 0, 3, 2, 0, 0, 0, 0, 0, 2, 0, 0, 2], [2, 2, 0, 0, 0, 0, 0, 4, 0, 3, 0, 0, 0, 2, 0, 0, 0, 0, 0, 2], [2, 0, 0, 1, 1, 1, 2, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 2], [1, 0, 0, 1, 0, 1, 1, 1, 0, 0, 0, 1, 1, 1, 4, 0, 0, 0, 0, 1], [1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 2], [2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 2], [2, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 0, 0, 2], [2, 0, 0, 0, 3, 0, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 4, 0, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 2, 3, 2, 3, 2], [2, 2, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "path": [[0, 7], [0, 8], [1, 8], [2, 8], [3, 7], [3, 6], [4, 6], [5, 6], [5, 7], [6, 7], [7, 7], [8, 8], [8, 9], [9, 9], [9, 8], [10, 8], [11, 8], [11, 7], [12, 7], [13, 7], [14, 8], [14, 9], [13, 9], [12, 9], [11, 9], [10, 10], [9, 10], [8, 10], [7, 10], [6, 9], [5, 9], [4, 9], [3, 9], [2, 9], [2, 10], [3, 10], [4, 10], [5, 10], [6, 10], [7, 11], [8, 11], [9, 11], [10, 11], [11, 11], [12, 11], [13, 11], [14, 11], [15, 10], [17, 9], [19, 7]]}}, "_generation_metadata": {"difficulty": 27, "difficulty_factors": {"difficulty": 27, "normalized_difficulty": 0.3, "base_count": 11.0, "base_delay": 93.0, "complexity": 0.51, "buildable_space": 0.665, "strategic_terrain_density": 0.28, "health_scale": 0.125, "speed_scale": 0.045, "obstacle_density": 0.33499999999999996}, "generation_timestamp": null, "algorithm_version": "1.0", "generation_method": "modular_ai_full", "modular_components": {"map_structure": "ai", "framework": "ai_guided_procedural", "economic_system": "ai", "wave_progression": "ai", "terrain_strategy": "ai", "enemy_strategy": "ai", "theme_and_naming": "ai"}, "creation_type": "🧩 FULL AI MAP GENERATION", "reliability": "high"}, "_ai_map_strategy": {"path_strategy": "maze", "path_length_target": 50, "path_complexity": 0.7, "terrain_strategy": "open", "buildable_space_target": 0.85, "strategic_focus": "learning", "layout_reasoning": "Given the player's beginner skill level and low performance on previous medium difficulty, a maze path with maximum buildable space is chosen to reduce time pressure and focus on learning tower placement strategies. The open terrain allows the player to experiment with tower diversity without strategic constraints."}, "_ai_metadata": {"economic_focus": "economy_boost", "economic_reasoning": "The player performed poorly on the previous difficulty level, indicating a need for significant economic assistance. Increased starting money and wave rewards provide immediate support, while adjustments to enemy reward scaling enhance long-term resource acquisition. Spawn rates are adjusted to optimize reward balance, particularly boosting under-rewarding enemies and slightly reducing over-rewarding enemy types. Tower cost progression is moderated to avoid overwhelming the player as they adapt to these changes."}, "_original_economic_values": {"starting_money": 200, "normal_wave_bonus": 65, "boss_wave_bonus": 240, "reward_per_wave": 0.144, "max_reward_multiplier": 22.0}, "_ai_wave_strategy": {"total_waves_modifier": 0.6, "enemy_health_scaling_modifier": 0.9, "enemy_speed_scaling_modifier": 0.9, "enemy_reward_scaling_modifier": 0.9, "enemy_size_scaling_modifier": 1.0, "enemy_damage_scaling_modifier": 0.9, "max_health_multiplier_modifier": 0.8, "max_speed_multiplier_modifier": 0.8, "max_reward_multiplier_modifier": 0.8, "max_size_multiplier_modifier": 0.9, "max_damage_multiplier_modifier": 0.8, "enemy_max_health_modifier": 0.8, "enemy_max_speed_modifier": 0.9, "min_spawn_delay_modifier": 1.2, "base_spawn_delay_modifier": 1.1, "spawn_delay_reduction_modifier": 1.1, "boss_wave_frequency_modifier": 0.85, "difficulty_modifier": 0.9, "wave_progression_reasoning": "To assist the player in achieving success after a low score of 6.0% at difficulty 50, the wave progression has been adjusted to provide a more manageable challenge. The total waves have been significantly reduced to allow the player to focus on mastering fewer challenges. Enemy scaling metrics such as health, speed, and damage have been decreased to make individual waves easier to survive. The frequency of boss waves has been slightly reduced to lessen the intensity of challenges. Adjustments to spawn delay modifiers increase the time between enemy spawns, giving the player more breathing room to strategize. Overall, modifiers have been set to enhance the player's opportunity to understand game mechanics and improve performance gradually."}, "_ai_terrain_strategy": {"path_type": "normal_path", "terrain_reasoning": "The player struggled with the previous map configuration and did not use tower diversity, indicating a need for easier terrain to develop strategic placement skills. A normal path will provide straightforward routes for enemies, encouraging the player to experiment with different towers and positions without overwhelming complexity."}, "_ai_enemy_strategy": {"primary_counter_enemies": ["BasicEnemy", "TankEnemy"], "strategy_focus": "Supportive challenges for beginner players with gentle counters", "extreme_spawn_preference": "late_game_focus", "spawn_distribution_style": "polarized_extreme"}, "enemy_buff_config": {"description": "AI-generated buff configuration based on general performance", "enabled": true, "scenario_type": "adaptive", "buff_intensity": "low", "custom_spawn_rates": {"wave_ranges": {"1-10": {"base_chance": 0.025, "max_buffs": 1, "allowed_buffs": ["speed_boost", "armor"]}, "11-20": {"base_chance": 0.075, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration"]}, "21-30": {"base_chance": 0.125, "max_buffs": 3, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]}, "31-40": {"base_chance": 0.175, "max_buffs": 4, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance"]}, "41-50": {"base_chance": 0.225, "max_buffs": 5, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}, "51+": {"base_chance": 0.3, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}}, "boss_multipliers": {"mini_boss": 1.0, "boss": 1.5, "super_boss": 2.0}}, "featured_combinations": ["stealth_assassin", "flying_fortress"], "buff_metrics_tracking": {"track_buff_effectiveness": true, "track_tower_counters": true, "track_player_adaptation": true}, "generation_metadata": {"generated_from": "general_performance", "intensity_reasoning": "Based on 6.0% score and loss", "fallback_config": true}}, "level_name": "Balancing Beginnings", "level_description": "Navigate through gently escalating challenges that encourage strategic support actions and provide opportunities for overcoming subtle obstacles. Designed to strengthen foundational skills with grace.", "_adaptive_metadata": {"ai_adjustments": {"difficulty_adjustment": {"new_difficulty": 27, "change": -3, "reasoning": "Initial difficulty based on performance score 6.0%"}, "map_structure_adjustments": {"path_strategy": "maze", "path_length_target": 50, "path_complexity": 0.7, "terrain_strategy": "open", "buildable_space_target": 0.85, "strategic_focus": "learning", "layout_reasoning": "Given the player's beginner skill level and low performance on previous medium difficulty, a maze path with maximum buildable space is chosen to reduce time pressure and focus on learning tower placement strategies. The open terrain allows the player to experiment with tower diversity without strategic constraints."}, "economic_adjustments": {"starting_money_modifier": 1.3, "normal_wave_bonus_modifier": 1.3, "boss_wave_bonus_modifier": 1.2, "enemy_reward_scaling_modifier": 1.2, "max_reward_multiplier_modifier": 1.1, "tower_cost_progression": {"early_increase_per_wave_modifier": 0.8, "mid_increase_per_wave_modifier": 0.7, "late_increase_per_wave_modifier": 0.5, "max_cost_multiplier_modifier": 0.8}, "enemy_spawn_rate_adjustments": {"FastEnemy": 0.7, "SpectralEnemy": 2.5, "VoidEnemy": 0.5, "AdaptiveEnemy": 2.0}, "strategic_focus": "economy_boost", "reasoning": "The player performed poorly on the previous difficulty level, indicating a need for significant economic assistance. Increased starting money and wave rewards provide immediate support, while adjustments to enemy reward scaling enhance long-term resource acquisition. Spawn rates are adjusted to optimize reward balance, particularly boosting under-rewarding enemies and slightly reducing over-rewarding enemy types. Tower cost progression is moderated to avoid overwhelming the player as they adapt to these changes."}, "wave_adjustments": {"total_waves_modifier": 0.6, "enemy_health_scaling_modifier": 0.9, "enemy_speed_scaling_modifier": 0.9, "enemy_reward_scaling_modifier": 0.9, "enemy_size_scaling_modifier": 1.0, "enemy_damage_scaling_modifier": 0.9, "max_health_multiplier_modifier": 0.8, "max_speed_multiplier_modifier": 0.8, "max_reward_multiplier_modifier": 0.8, "max_size_multiplier_modifier": 0.9, "max_damage_multiplier_modifier": 0.8, "enemy_max_health_modifier": 0.8, "enemy_max_speed_modifier": 0.9, "min_spawn_delay_modifier": 1.2, "base_spawn_delay_modifier": 1.1, "spawn_delay_reduction_modifier": 1.1, "boss_wave_frequency_modifier": 0.85, "difficulty_modifier": 0.9, "wave_progression_reasoning": "To assist the player in achieving success after a low score of 6.0% at difficulty 50, the wave progression has been adjusted to provide a more manageable challenge. The total waves have been significantly reduced to allow the player to focus on mastering fewer challenges. Enemy scaling metrics such as health, speed, and damage have been decreased to make individual waves easier to survive. The frequency of boss waves has been slightly reduced to lessen the intensity of challenges. Adjustments to spawn delay modifiers increase the time between enemy spawns, giving the player more breathing room to strategize. Overall, modifiers have been set to enhance the player's opportunity to understand game mechanics and improve performance gradually."}, "terrain_adjustments": {"path_type": "normal_path", "terrain_reasoning": "The player struggled with the previous map configuration and did not use tower diversity, indicating a need for easier terrain to develop strategic placement skills. A normal path will provide straightforward routes for enemies, encouraging the player to experiment with different towers and positions without overwhelming complexity."}, "enemy_adjustments": {"primary_counter_enemies": ["BasicEnemy", "TankEnemy"], "strategy_focus": "Supportive challenges for beginner players with gentle counters", "extreme_spawn_preference": "late_game_focus", "spawn_distribution_style": "polarized_extreme"}, "reasoning": "Complete AI-driven generation: Map structure, economic system, wave progression, terrain, enemies, and theming all designed by AI for performance score 6.0% (Final difficulty: 27)", "buff_system_adjustments": {"enabled": true, "intensity": "low", "reasoning": "AI-generated buff configuration"}}, "generation_timestamp": "2025-07-09T00:38:02.006409", "generation_method": "modular_ai_multi_game", "creation_type": "🧩 FULL AI MAP GENERATION", "multi_game_context": {"games_analyzed": 1, "avg_score": 6.0, "win_rate": 0.0, "trend": "stable", "difficulty_progression": [50], "performance_summaries": [{"score": 6.0, "win_flag": false, "lives_remaining": 0, "starting_lives": 20, "towers_built": {}, "previous_config_details": {}}]}}, "_analytical_balancing": {"applied": true, "problematic_waves": 1, "waves_analyzed": 20, "tower_catalog_size": 14, "path_length": 54.96399003885764, "enemy_speed": 1.0}}