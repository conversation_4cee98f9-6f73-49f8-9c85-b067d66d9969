"""
Adjustment Strategies Module for Tower Defense Adaptive AI

Contains fallback adjustment methods and strategic logic.
"""

from typing import Dict, Any
from .performance_analysis import PerformanceData


def create_fallback_adjustments(performance: PerformanceData) -> Dict[str, Any]:
    """Create reasonable adjustments if AI fails"""
    score = performance.score
    win_flag = performance.win_flag
    
    # FIXED: Prioritize win/loss status over score percentage
    if win_flag:
        # Player WON - can increase difficulty based on performance
        if score >= 80:
            difficulty_change = 15
            economic_modifier = 0.9  # Less money
            enemy_modifier = 1.2  # Stronger enemies
        elif score >= 60:
            difficulty_change = 8
            economic_modifier = 0.95
            enemy_modifier = 1.1
        elif score >= 40:
            difficulty_change = 5
            economic_modifier = 1.0
            enemy_modifier = 1.0
        else:
            difficulty_change = 2
            economic_modifier = 1.05  # Slightly more money for poor wins
            enemy_modifier = 0.95  # Slightly weaker enemies
    else:
        # Player LOST - always reduce difficulty to help them succeed
        if score >= 70:
            difficulty_change = -5
            economic_modifier = 1.1  # More money
            enemy_modifier = 0.9  # Weaker enemies
        elif score >= 50:
            difficulty_change = -10
            economic_modifier = 1.2
            enemy_modifier = 0.85
        elif score >= 30:
            difficulty_change = -15
            economic_modifier = 1.3
            enemy_modifier = 0.8
        else:
            difficulty_change = -20
            economic_modifier = 1.4
            enemy_modifier = 0.7
    
    # Get current difficulty from previous config
    current_difficulty = 50  # Default
    if '_generation_metadata' in performance.previous_config:
        current_difficulty = performance.previous_config['_generation_metadata'].get('difficulty', 50)
    
    # Difficulty-adjusted performance evaluation
    # A 40% score on difficulty 80 is much better than 40% on difficulty 20
    difficulty_adjusted_score = score * (1 + (current_difficulty - 50) / 200)  # Boost score for harder difficulties
    
    # Recalculate difficulty change based on adjusted score
    if difficulty_adjusted_score >= 80:
        difficulty_change = 15
    elif difficulty_adjusted_score >= 60:
        difficulty_change = 8
    elif difficulty_adjusted_score >= 40:
        difficulty_change = 2
    elif difficulty_adjusted_score >= 20:
        difficulty_change = -5
    else:
        difficulty_change = -15
    
    new_difficulty = max(1, min(100, current_difficulty + difficulty_change))
    
    # Calculate wave count adjustment
    wave_modifier = 1.0
    wave_explanation = "Standard wave count"
    
    if score >= 80:
        wave_modifier = 1.25  # 25% more waves for high performers (100 waves max)
        wave_explanation = "More waves for high performance - expert level challenge"
    elif score >= 60:
        wave_modifier = 1.1  # 10% more waves
        wave_explanation = "More waves for good performance - intermediate challenge"
    elif score >= 40:
        wave_modifier = 1.0  # Standard wave count
        wave_explanation = "Standard wave count for average performance"
    elif score >= 20:
        wave_modifier = 0.7  # 30% fewer waves
        wave_explanation = "Fewer waves to make it easier for struggling players"
    else:
        wave_modifier = 0.5  # 50% fewer waves
        wave_explanation = "Much fewer waves for beginners - focus on learning"
    
    return {
        "level_name": f"Adaptive Challenge {new_difficulty}",
        "difficulty_adjustment": {
            "explanation": f"Score {score:.1f} suggests {'increasing' if difficulty_change > 0 else 'decreasing'} difficulty",
            "new_difficulty": new_difficulty
        },
        "wave_adjustments": {
            "total_waves_modifier": wave_modifier,
            "explanation": wave_explanation
        },
        "economic_adjustments": {
            "starting_money_modifier": economic_modifier,
            "enemy_rewards_modifier": economic_modifier
        },
        "enemy_adjustments": {
            "health_modifier": enemy_modifier,
            "speed_modifier": 1.0,
            "count_modifier": enemy_modifier
        },
        "enemy_buffs": {
            "max_health_modifier": enemy_modifier,
            "max_speed_modifier": 1.0 if win_flag else 0.9,  # Slightly reduce speed for losses
            "explanation": f"AI fallback buffs: health {enemy_modifier:.2f}x, speed {'1.0x' if win_flag else '0.9x'} based on performance"
        },
        "map_adjustments": {
            "complexity_modifier": 1.0,
            "obstacle_density_modifier": 1.0,
            "terrain_strategy": "Balanced terrain mix suitable for learning"
        },
        "tower_adjustments": {
            "cost_modifier": 1.0 / economic_modifier,  # Inverse relationship
            "damage_modifier": 1.0
        },
        "rock_removal_adjustments": {
            "base_cost": max(200, min(600, 300 + (new_difficulty * 2))),  # Scale with difficulty
            "wave_scaling_factor": 0.08 + (new_difficulty / 1000.0),  # Slight scaling increase with difficulty
            "explanation": f"Hefty rock removal costs scaled to difficulty {new_difficulty}"
        },
        "special_mechanics": [],
        "reasoning": f"Difficulty-adjusted fallback: {score:.1f}% on difficulty {current_difficulty} (adjusted score: {difficulty_adjusted_score:.1f}%). {wave_explanation} considering previous challenge level."
    }


def create_multi_game_fallback_adjustments(latest_performance: PerformanceData, 
                                          avg_score: float, trend: str) -> Dict[str, Any]:
    """Create fallback adjustments based on multi-game analysis"""
    
    # FIXED: Consider win/loss status of latest performance
    latest_win = latest_performance.win_flag
    
    # Base adjustment on average score and latest win status
    if latest_win:
        # Latest game was won - can increase difficulty
        if avg_score >= 80:
            base_difficulty_change = 15
            economic_modifier = 0.9
            enemy_modifier = 1.2
        elif avg_score >= 60:
            base_difficulty_change = 8
            economic_modifier = 0.95
            enemy_modifier = 1.1
        elif avg_score >= 40:
            base_difficulty_change = 5
            economic_modifier = 1.0
            enemy_modifier = 1.0
        else:
            base_difficulty_change = 2
            economic_modifier = 1.05
            enemy_modifier = 0.95
    else:
        # Latest game was lost - reduce difficulty regardless of average
        if avg_score >= 70:
            base_difficulty_change = -5
            economic_modifier = 1.1
            enemy_modifier = 0.9
        elif avg_score >= 50:
            base_difficulty_change = -10
            economic_modifier = 1.2
            enemy_modifier = 0.85
        elif avg_score >= 30:
            base_difficulty_change = -15
            economic_modifier = 1.3
            enemy_modifier = 0.8
        else:
            base_difficulty_change = -20
            economic_modifier = 1.4
            enemy_modifier = 0.7
    
    # Get current difficulty
    current_difficulty = 50
    if '_generation_metadata' in latest_performance.previous_config:
        current_difficulty = latest_performance.previous_config['_generation_metadata'].get('difficulty', 50)
    
    # Multi-game difficulty-adjusted performance evaluation
    # Consider both average score and the difficulty context
    difficulty_adjusted_avg_score = avg_score * (1 + (current_difficulty - 50) / 200)
    
    # Recalculate base difficulty change based on adjusted score
    if difficulty_adjusted_avg_score >= 80:
        base_difficulty_change = 15
    elif difficulty_adjusted_avg_score >= 60:
        base_difficulty_change = 8
    elif difficulty_adjusted_avg_score >= 40:
        base_difficulty_change = 2
    elif difficulty_adjusted_avg_score >= 20:
        base_difficulty_change = -5
    else:
        base_difficulty_change = -15
    
    # Adjust based on trend
    if trend == "improving":
        base_difficulty_change += 5  # More aggressive increase
    elif trend == "declining":
        base_difficulty_change -= 5  # More conservative/helpful
    
    new_difficulty = max(1, min(100, current_difficulty + base_difficulty_change))
    
    # Calculate wave count adjustment based on multi-game performance
    wave_modifier = 1.0
    wave_explanation = "Standard wave count"
    
    if avg_score >= 80:
        wave_modifier = 1.25  # 25% more waves for consistently high performers (100 waves max)
        wave_explanation = "More waves for consistently high performance - expert level challenge"
    elif avg_score >= 60:
        wave_modifier = 1.1  # 10% more waves for consistently good players
        wave_explanation = "More waves for consistently good performance - advanced challenge"
    elif avg_score >= 40:
        wave_modifier = 1.0  # Standard wave count
        wave_explanation = "Standard wave count for average performance"
    elif avg_score >= 20:
        wave_modifier = 0.7  # 30% fewer waves for consistent struggles
        wave_explanation = "Fewer waves due to consistent difficulty - easier progression"
    else:
        wave_modifier = 0.5  # 50% fewer waves for consistently poor performance
        wave_explanation = "Much fewer waves for consistently struggling players - beginner friendly"
    
    # Adjust based on trend
    if trend == "improving":
        wave_modifier += 0.1  # Slight increase for improvement
        wave_explanation += " (increased due to improving trend)"
    elif trend == "declining":
        wave_modifier -= 0.1  # Reduction for declining performance
        wave_explanation += " (reduced due to declining trend)"
    
    wave_modifier = max(0.2, min(1.25, wave_modifier))  # Clamp between 20% and 125% (max 100 waves)
    
    # Generate appropriate level name for multi-game analysis
    if trend == "improving":
        level_name = f"Improvement Challenge {new_difficulty}"
    elif trend == "declining":
        level_name = f"Stabilization Training {new_difficulty}"
    else:
        level_name = f"Consistency Trial {new_difficulty}"
    
    return {
        "level_name": level_name,
        "difficulty_adjustment": {
            "explanation": f"Multi-game analysis: avg score {avg_score:.1f}, trend {trend}",
            "new_difficulty": new_difficulty
        },
        "wave_adjustments": {
            "total_waves_modifier": wave_modifier,
            "explanation": wave_explanation
        },
        "economic_adjustments": {
            "starting_money_modifier": economic_modifier,
            "enemy_rewards_modifier": economic_modifier
        },
        "enemy_adjustments": {
            "health_modifier": enemy_modifier,
            "speed_modifier": 1.0,
            "count_modifier": enemy_modifier
        },
        "enemy_buffs": {
            "max_health_modifier": enemy_modifier,
            "max_speed_modifier": 1.0 if latest_win else 0.85,  # More speed reduction for multi-game losses
            "explanation": f"Multi-game AI fallback buffs: health {enemy_modifier:.2f}x, speed {'1.0x' if latest_win else '0.85x'} based on performance trend"
        },
        "map_adjustments": {
            "complexity_modifier": 1.0,
            "obstacle_density_modifier": 1.0,
            "terrain_strategy": "Multi-game adaptive terrain strategy"
        },
        "tower_adjustments": {
            "cost_modifier": 1.0 / economic_modifier,
            "damage_modifier": 1.0
        },
        "rock_removal_adjustments": {
            "base_cost": max(250, min(700, 350 + (new_difficulty * 2.5))),  # Multi-game gets slightly higher costs
            "wave_scaling_factor": 0.09 + (new_difficulty / 800.0),  # Slightly more aggressive scaling
            "explanation": f"Multi-game analyzed hefty rock removal costs for difficulty {new_difficulty}"
        },
        "special_mechanics": [],
        "reasoning": f"Difficulty-adjusted multi-game fallback: {avg_score:.1f}% on difficulty {current_difficulty} (adjusted: {difficulty_adjusted_avg_score:.1f}%), trend {trend}. {wave_explanation} Counter-strategies consider both performance and previous challenge level."
    }


def analyze_terrain_distribution(terrain_grid: list) -> str:
    """Analyze terrain distribution and strategic implications"""
    if not terrain_grid:
        return "Terrain: Unknown"
    
    # Count terrain types
    terrain_counts = {}
    total_cells = 0
    for row in terrain_grid:
        for cell in row:
            terrain_counts[cell] = terrain_counts.get(cell, 0) + 1
            total_cells += 1
    
    # Terrain type mapping with strategic info
    terrain_info = {
        0: "Grass (Standard tower placement)",
        1: "Path (Enemy route - no towers)", 
        2: "Rock (Impassable obstacles - no towers)",
        3: "Water (Freezer/Splash towers only - enhances freeze effects +50%)",
        4: "Forest (All towers - reduces range 20%, increases damage 30%)",
        5: "Sand (All towers - enemies move 50% faster)"
    }
    
    analysis_parts = ["Terrain Distribution:"]
    for terrain_type, count in terrain_counts.items():
        percentage = (count / total_cells) * 100
        terrain_name = terrain_info.get(terrain_type, f"Unknown terrain {terrain_type}")
        analysis_parts.append(f"  {terrain_name}: {percentage:.1f}%")
    
    return "\n".join(analysis_parts)


def summarize_config(config: Dict[str, Any]) -> str:
    """Create a summary of the config for AI analysis"""
    try:
        summary_parts = []
        
        # Game basics
        if 'game_config' in config:
            game_cfg = config['game_config']
            summary_parts.append(f"Starting Money: {game_cfg.get('starting_money', 'unknown')}")
            summary_parts.append(f"Starting Lives: {game_cfg.get('starting_lives', 'unknown')}")
        
        # Wave config with total waves
        if 'wave_config' in config:
            wave_cfg = config['wave_config']
            if 'spawn_config' in wave_cfg:
                spawn_cfg = wave_cfg['spawn_config']
                summary_parts.append(f"Base Enemy Count: {spawn_cfg.get('base_enemy_count', 'unknown')}")
                summary_parts.append(f"Base Spawn Delay: {spawn_cfg.get('base_spawn_delay', 'unknown')}")
            
            if 'enemy_scaling' in wave_cfg:
                scaling = wave_cfg['enemy_scaling']
                summary_parts.append(f"Health Scaling: {scaling.get('health_per_wave', 'unknown')} per wave")
                summary_parts.append(f"Speed Scaling: {scaling.get('speed_per_wave', 'unknown')} per wave")
            
            # Add total waves information
            if 'total_waves' in wave_cfg:
                summary_parts.append(f"Total Waves: {wave_cfg['total_waves']}")
        
        # Map info with terrain analysis
        if 'map_config' in config and 'default_map' in config['map_config']:
            map_cfg = config['map_config']['default_map']
            summary_parts.append(f"Map Size: {map_cfg.get('width', '?')}x{map_cfg.get('height', '?')}")
            if 'path' in map_cfg:
                summary_parts.append(f"Path Length: {len(map_cfg['path'])} cells")
            
            # Analyze terrain distribution
            if 'terrain' in map_cfg:
                terrain_analysis = analyze_terrain_distribution(map_cfg['terrain'])
                summary_parts.append(terrain_analysis)
        
        # Tower costs
        if 'tower_config' in config and 'base_costs' in config['tower_config']:
            costs = config['tower_config']['base_costs']
            avg_cost = sum(costs.values()) / len(costs) if costs else 0
            summary_parts.append(f"Average Tower Cost: {avg_cost:.1f}")
        
        # Difficulty metadata
        if '_generation_metadata' in config:
            meta = config['_generation_metadata']
            if 'difficulty' in meta:
                summary_parts.append(f"Generated Difficulty: {meta['difficulty']}")
        
        return "\n".join(summary_parts)
        
    except Exception as e:
        return f"Config summary unavailable: {e}" 