"""
Tower Data Management - Centralized tower information
"""

from .tower_categories import TowerCategoryManager, TowerCategory
from typing import Dict, List, Optional

class TowerDataManager:
    """Manages tower information and provides data to UI and game systems"""
    
    def __init__(self, tower_manager):
        self.tower_manager = tower_manager
        
        # Initialize category manager
        self.category_manager = TowerCategoryManager()
        
        # Tower type mapping (order matters for UI display)
        self.tower_types = [
            'basic', 'sniper', 'freezer', 'detector', 'antiair', 'poison', 
            'laser', 'cannon', 'lightning', 'flame', 'ice', 'explosive', 
            'missile', 'splash', 'destroyer'
        ]
        
        # Tower display names
        self.tower_names = {
            'basic': 'Basic Tower',
            'sniper': 'Sniper Tower', 
            'freezer': 'Freezer Tower',
            'detector': 'Detector Tower',
            'antiair': 'Anti-Air Tower',
            'poison': 'Poison Tower',
            'laser': 'Laser Tower',
            'cannon': 'Cannon Tower',
            'lightning': 'Lightning Tower',
            'flame': 'Flame Tower',
            'ice': 'Ice Tower',
            'explosive': 'Explosive Tower',
            'missile': 'Missile Tower',
            'splash': 'Splash Tower',
            'destroyer': 'Navy Destroyer'
        }
        
        # Tower descriptions
        self.tower_descriptions = {
            'basic': 'Cheap starter tower',
            'sniper': 'Long range, high damage',
            'freezer': 'Completely freezes single enemies',
            'detector': 'Reveals invisible enemies',
            'antiair': 'Targets flying enemies',
            'poison': 'Poison damage over time',
            'laser': 'Pierces through enemies',
            'cannon': 'POWERFUL siege artillery with massive splash damage',
            'lightning': 'Chains between enemies',
            'flame': 'Cone attack with burn',
            'ice': 'Area freeze, no damage',
            'explosive': 'DEVASTATING rocket launcher with huge explosions',
            'missile': 'Homing missiles, AOE, 2x2 size',
            'splash': 'Water-only, makes enemies wet',
            'destroyer': 'WATER-ONLY: Extreme range naval artillery'
        }
        
        # Tower colors for UI display
        self.tower_colors = {
            'basic': (0, 200, 0),
            'sniper': (0, 0, 255),
            'freezer': (0, 255, 255),
            'detector': (255, 255, 0),
            'antiair': (0, 191, 255),
            'poison': (50, 205, 50),
            'laser': (255, 0, 255),
            'cannon': (139, 69, 19),
            'lightning': (255, 255, 0),
            'flame': (255, 69, 0),
            'ice': (173, 216, 230),
            'explosive': (255, 165, 0),
            'missile': (128, 128, 128),
            'splash': (30, 144, 255),
            'destroyer': (70, 130, 180)
        }
    
    def get_tower_count(self) -> int:
        """Get total number of tower types"""
        return len(self.tower_types)
    
    def get_tower_type_by_index(self, index: int) -> Optional[str]:
        """Get tower type by index"""
        if 0 <= index < len(self.tower_types):
            return self.tower_types[index]
        return None
    
    def get_tower_data(self, tower_type: str) -> Optional[dict]:
        """Get complete tower data for UI display"""
        if tower_type not in self.tower_types:
            return None
            
        # Get cost from tower manager
        cost = self.tower_manager.get_tower_cost(tower_type)
        
        # Get tower stats without creating instances (avoids debug spam)
        tower_classes = self.tower_manager._get_tower_classes()
        tower_class = tower_classes.get(tower_type)
        if tower_class and hasattr(tower_class, 'get_static_stats'):
            # Use static method if available (preferred)
            stats = tower_class.get_static_stats()
        elif tower_class:
            # Fallback: Create temporary instance to read stats (legacy)
            temp_tower = tower_class(0, 0)
            stats = self._extract_tower_stats(temp_tower)
        else:
            stats = {}
        
        return {
            'type': tower_type,
            'name': self.tower_names.get(tower_type, tower_type.title()),
            'cost': cost,
            'color': self.tower_colors.get(tower_type, (128, 128, 128)),
            'description': self.tower_descriptions.get(tower_type, 'Tower'),
            'stats': stats
        }
    
    def get_all_tower_data(self) -> list:
        """Get data for all towers for UI display"""
        return [self.get_tower_data(tower_type) for tower_type in self.tower_types]
    
    def _extract_tower_stats(self, tower) -> dict:
        """Extract displayable stats from a tower instance"""
        stats = {}
        
        # Damage
        if hasattr(tower, 'damage'):
            stats['Damage'] = tower.damage
        
        # Range
        if hasattr(tower, 'range'):
            stats['Range'] = tower.range
            
        # Speed (convert fire rate to readable format)
        if hasattr(tower, 'fire_rate'):
            if tower.fire_rate <= 20:
                speed = 'Very Fast'
            elif tower.fire_rate <= 35:
                speed = 'Fast'
            elif tower.fire_rate <= 50:
                speed = 'Medium'
            elif tower.fire_rate <= 70:
                speed = 'Slow'
            elif tower.fire_rate <= 100:
                speed = 'Very Slow'
            else:
                speed = 'Ultra Slow'
            stats['Speed'] = speed
        
        return stats
    
    # === Category Management Methods ===
    
    def get_tower_category(self, tower_type: str) -> List[TowerCategory]:
        """Get the category(ies) for a specific tower type"""
        return self.category_manager.get_category_for_tower(tower_type)
    
    def get_primary_tower_category(self, tower_type: str) -> Optional[TowerCategory]:
        """Get the primary category for a tower"""
        return self.category_manager.get_primary_category_for_tower(tower_type)
    
    def get_towers_in_category(self, category: TowerCategory) -> List[str]:
        """Get all towers in a specific category"""
        return list(self.category_manager.get_towers_in_category(category))
    
    def get_category_info(self, category: TowerCategory) -> Dict:
        """Get complete information about a category"""
        return self.category_manager.get_category_info(category)
    
    def get_all_categories(self) -> List[TowerCategory]:
        """Get all available categories"""
        return self.category_manager.get_all_categories()
    
    def get_towers_by_category(self) -> Dict[TowerCategory, List[str]]:
        """Get all towers organized by category"""
        result = {}
        for category, towers in self.category_manager.get_towers_by_category().items():
            result[category] = list(towers)
        return result
    
    def get_category_stats(self) -> Dict[TowerCategory, Dict]:
        """Get statistics about each category"""
        return self.category_manager.get_category_stats()
    
    def get_categorized_tower_data(self) -> Dict[TowerCategory, List[dict]]:
        """Get all tower data organized by category"""
        result = {}
        for category in self.get_all_categories():
            result[category] = []
            for tower_type in self.get_towers_in_category(category):
                tower_data = self.get_tower_data(tower_type)
                if tower_data:
                    # Add category information to tower data
                    tower_data['category'] = category
                    tower_data['category_info'] = self.get_category_info(category)
                    result[category].append(tower_data)
        return result
    
    def get_enhanced_tower_data(self, tower_type: str) -> Optional[dict]:
        """Get tower data with category information included"""
        tower_data = self.get_tower_data(tower_type)
        if not tower_data:
            return None
        
        # Add category information
        categories = self.get_tower_category(tower_type)
        primary_category = self.get_primary_tower_category(tower_type)
        
        tower_data['categories'] = categories
        tower_data['primary_category'] = primary_category
        
        if primary_category:
            tower_data['category_info'] = self.get_category_info(primary_category)
            tower_data['category_color'] = self.category_manager.get_category_color(primary_category)
            tower_data['category_icon'] = self.category_manager.get_category_icon(primary_category)
        
        return tower_data
    
    def get_all_enhanced_tower_data(self) -> List[dict]:
        """Get data for all towers with category information"""
        result = []
        for tower_type in self.tower_types:
            tower_data = self.get_enhanced_tower_data(tower_type)
            if tower_data:
                result.append(tower_data)
        return result
    
    def validate_tower_categorization(self) -> Dict[str, List[str]]:
        """Validate that all towers are properly categorized"""
        return self.category_manager.validate_categorization(set(self.tower_types))
    
    def print_category_breakdown(self):
        """Print a detailed breakdown of all categories"""
        self.category_manager.print_category_breakdown()
        
        # Also print validation results
        validation = self.validate_tower_categorization()
        if validation['warnings']:
            print("\n=== CATEGORIZATION WARNINGS ===")
            for warning in validation['warnings']:
                print(f"⚠️  {warning}")
        
        if validation['uncategorized']:
            print(f"\n🔍 Uncategorized towers: {', '.join(validation['uncategorized'])}")
        
        if validation['multi_category']:
            print(f"\n📊 Multi-category towers: {', '.join(validation['multi_category'])}")
    
    def is_tower_in_category(self, tower_type: str, category: TowerCategory) -> bool:
        """Check if a tower belongs to a specific category"""
        return self.category_manager.is_tower_in_category(tower_type, category) 