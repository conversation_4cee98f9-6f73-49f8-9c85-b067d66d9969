"""
Performance Renderer for Tower Defense Game Launcher
Handles drawing of performance statistics panels
"""

import pygame
from typing import Dict, List, Any, Optional


class PerformanceRenderer:
    """Handles drawing of performance statistics"""
    
    def __init__(self, colors, fonts, screen_width: int, screen_height: int):
        """Initialize with color scheme, fonts, and screen dimensions"""
        self.colors = colors
        self.fonts = fonts
        self.screen_width = screen_width
        self.screen_height = screen_height
    
    def draw_performance_panel(self, screen, performance_stats):
        """Draw the modern performance statistics panel"""
        if not performance_stats:
            return
        
        # Modern panel dimensions and positioning
        panel_width = 380
        panel_height = 320
        panel_x = self.screen_width - panel_width - 30
        panel_y = 140
        
        # Panel background with shadow effect
        shadow_offset = 4
        pygame.draw.rect(screen, (0, 0, 0, 30), (panel_x + shadow_offset, panel_y + shadow_offset, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(screen, self.colors['CARD_BG'], (panel_x, panel_y, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(screen, self.colors['BORDER_LIGHT'], (panel_x, panel_y, panel_width, panel_height), 2, border_radius=12)
        
        # Header
        header_height = 50
        pygame.draw.rect(screen, self.colors['ACCENT_BLUE'], (panel_x, panel_y, panel_width, header_height), border_radius=12)
        pygame.draw.rect(screen, self.colors['ACCENT_BLUE'], (panel_x, panel_y + header_height - 12, panel_width, 12))
        
        title_text = self.fonts['subtitle'].render("Performance Stats", True, self.colors['CARD_BG'])
        title_rect = title_text.get_rect(center=(panel_x + panel_width // 2, panel_y + header_height // 2))
        screen.blit(title_text, title_rect)
        
        # Content area
        content_x = panel_x + 20
        content_y = panel_y + header_height + 20
        stats = performance_stats
        
        # Games analyzed
        games_text = self.fonts['menu'].render(f"Games Analyzed: {stats['games_count']}", True, self.colors['TEXT_PRIMARY'])
        screen.blit(games_text, (content_x, content_y))
        content_y += 30
        
        # Average score with modern progress bar
        avg_text = self.fonts['menu'].render("Average Score", True, self.colors['TEXT_PRIMARY'])
        screen.blit(avg_text, (content_x, content_y))
        
        score_text = self.fonts['menu'].render(f"{stats['average_score']:.1f}%", True, self.colors['ACCENT_BLUE'])
        score_rect = score_text.get_rect(right=panel_x + panel_width - 20)
        score_rect.y = content_y
        screen.blit(score_text, score_rect)
        
        content_y += 25
        self._draw_difficulty_bar(screen, content_x, content_y, stats['average_score'], panel_width - 40, 8)
        content_y += 25
        
        # Win rate
        win_text = self.fonts['menu'].render("Win Rate", True, self.colors['TEXT_PRIMARY'])
        screen.blit(win_text, (content_x, content_y))
        
        win_rate_text = self.fonts['menu'].render(f"{stats['win_rate']:.1f}%", True, self.colors['ACCENT_GREEN'])
        win_rate_rect = win_rate_text.get_rect(right=panel_x + panel_width - 20)
        win_rate_rect.y = content_y
        screen.blit(win_rate_text, win_rate_rect)
        content_y += 25
        
        self._draw_difficulty_bar(screen, content_x, content_y, stats['win_rate'], panel_width - 40, 8)
        content_y += 35
        
        # Trend with icon
        trend_text = self.fonts['menu'].render("Trend:", True, self.colors['TEXT_PRIMARY'])
        screen.blit(trend_text, (content_x, content_y))
        
        trend_color = self.colors['ACCENT_GREEN'] if stats['trend'] == 'improving' else (220, 53, 69) if stats['trend'] == 'declining' else self.colors['TEXT_SECONDARY']
        trend_icon = "↗" if stats['trend'] == 'improving' else "↘" if stats['trend'] == 'declining' else "→"
        trend_value = self.fonts['menu'].render(f"{trend_icon} {stats['trend'].title()}", True, trend_color)
        trend_rect = trend_value.get_rect(right=panel_x + panel_width - 20)
        trend_rect.y = content_y
        screen.blit(trend_value, trend_rect)
        content_y += 40
        
        # Recent games section
        games_header = self.fonts['menu'].render("Recent Games", True, self.colors['TEXT_PRIMARY'])
        screen.blit(games_header, (content_x, content_y))
        content_y += 25
        
        # Game history list
        for i, game in enumerate(stats['performance_history'][:4]):  # Show up to 4 games
            if content_y >= panel_y + panel_height - 30:
                break
                
            game_num = game['game_number']
            score = game['score']
            won = game['won']
            
            # Game item background
            item_height = 22
            item_bg_color = self.colors['HOVER_BG'] if i % 2 == 0 else self.colors['CARD_BG']
            pygame.draw.rect(screen, item_bg_color, (content_x - 5, content_y - 2, panel_width - 30, item_height))
            
            # Game info
            game_text = self.fonts['info'].render(f"Game {game_num}", True, self.colors['TEXT_PRIMARY'])
            screen.blit(game_text, (content_x, content_y))
            
            score_text = self.fonts['info'].render(f"{score:.0f}%", True, self.colors['TEXT_SECONDARY'])
            screen.blit(score_text, (content_x + 80, content_y))
            
            status_color = self.colors['ACCENT_GREEN'] if won else (220, 53, 69)
            status_text = self.fonts['info'].render("Won" if won else "Lost", True, status_color)
            status_rect = status_text.get_rect(right=panel_x + panel_width - 25)
            status_rect.y = content_y
            screen.blit(status_text, status_rect)
            
            content_y += item_height + 2
    
    def draw_no_performance_data_message(self, screen):
        """Draw a message when no performance data is available"""
        # Panel dimensions and positioning (same as performance panel)
        panel_width = 380
        panel_height = 200
        panel_x = self.screen_width - panel_width - 30
        panel_y = 140
        
        # Panel background with shadow effect
        shadow_offset = 4
        pygame.draw.rect(screen, (0, 0, 0, 30), (panel_x + shadow_offset, panel_y + shadow_offset, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(screen, self.colors['CARD_BG'], (panel_x, panel_y, panel_width, panel_height), border_radius=12)
        pygame.draw.rect(screen, self.colors['BORDER_LIGHT'], (panel_x, panel_y, panel_width, panel_height), 2, border_radius=12)
        
        # Header
        header_height = 50
        pygame.draw.rect(screen, self.colors['ACCENT_ORANGE'], (panel_x, panel_y, panel_width, header_height), border_radius=12)
        pygame.draw.rect(screen, self.colors['ACCENT_ORANGE'], (panel_x, panel_y + header_height - 12, panel_width, 12))
        
        title_text = self.fonts['subtitle'].render("No Performance Data", True, self.colors['CARD_BG'])
        title_rect = title_text.get_rect(center=(panel_x + panel_width // 2, panel_y + header_height // 2))
        screen.blit(title_text, title_rect)
        
        # Content area
        content_x = panel_x + 20
        content_y = panel_y + header_height + 30
        
        # Message
        message_lines = [
            "No game data found.",
            "",
            "Play some games first to",
            "generate performance",
            "statistics and enable",
            "adaptive AI features."
        ]
        
        for i, line in enumerate(message_lines):
            if line:  # Skip empty lines
                message_text = self.fonts['menu'].render(line, True, self.colors['TEXT_PRIMARY'])
                screen.blit(message_text, (content_x, content_y + i * 20))
    
    def _draw_difficulty_bar(self, screen, x: int, y: int, difficulty: int, width: int = 100, height: int = 6):
        """Draw a modern, sleek difficulty bar"""
        if not isinstance(difficulty, (int, float)):
            difficulty = 50
        difficulty = max(0, min(100, int(difficulty)))
        
        # Background bar
        pygame.draw.rect(screen, self.colors['BORDER_LIGHT'], (x, y, width, height), border_radius=height//2)
        
        # Progress fill
        fill_width = int((difficulty / 100) * width)
        if fill_width > 0:
            if difficulty <= 30:
                color = self.colors['ACCENT_GREEN']
            elif difficulty <= 70:
                color = self.colors['ACCENT_ORANGE']
            else:
                color = (220, 53, 69)  # Red for high difficulty
            
            pygame.draw.rect(screen, color, (x, y, fill_width, height), border_radius=height//2)
