import pygame
import math
from typing import Dict, List, Tuple, Optional
from .tower_data import TowerDataManager
from .ui_renderer import <PERSON><PERSON><PERSON><PERSON>
from .enemy_info_lookup import EnemyInfoLookup

class UIManager:
    """Manages UI state and coordinates with renderer and data manager"""
    
    def __init__(self, screen_width: int, screen_height: int, tower_manager):
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Initialize sub-systems
        self.tower_data_manager = TowerDataManager(tower_manager)
        self.renderer = UIRenderer(screen_width, screen_height)
        self.enemy_lookup = EnemyInfoLookup(screen_width, screen_height)
        
        # UI State
        self.selected_tower_index = None
        self.hovered_tower_index = None
        self.selected_placed_tower = None
        self.mouse_pos = (0, 0)
        
        # Rock removal state
        self.rock_removal_mode = False
        
        # Scrolling state
        self.scroll_offset = 0
        self.max_scroll = self._calculate_max_scroll()
    
    def _calculate_max_scroll(self) -> int:
        """Calculate maximum scroll offset"""
        tower_count = self.tower_data_manager.get_tower_count()
        if tower_count == 0:
            return 0
        
        # Calculate total width using the same formula as the drawing logic
        # Each tower takes (tower_slot_width + tower_slot_margin) space
        total_width = tower_count * (self.renderer.tower_slot_width + self.renderer.tower_slot_margin)
        
        # Account for the left and right margins (20px each)
        visible_width = self.screen_width - 40
        
        # Max scroll is how much we need to scroll to see all towers FULLY
        # We need to subtract the margin from the last tower since it doesn't need trailing space
        adjusted_total_width = total_width - self.renderer.tower_slot_margin
        max_scroll = max(0, adjusted_total_width - visible_width)
        
        return max_scroll
    
    def update_mouse_pos(self, pos: Tuple[int, int]):
        """Update mouse position for hover effects"""
        self.mouse_pos = pos
        self._update_hover_state()
    
    def _update_hover_state(self):
        """Update which tower slot is being hovered"""
        mouse_x, mouse_y = self.mouse_pos
        
        # Check if mouse is in tower bar area
        if self.renderer.bottom_bar_y <= mouse_y <= self.screen_height:
            # Calculate which tower slot is hovered
            relative_x = mouse_x - 20 + self.scroll_offset  # 20 is left margin
            slot_index = int(relative_x // (self.renderer.tower_slot_width + self.renderer.tower_slot_margin))
            
            if 0 <= slot_index < self.tower_data_manager.get_tower_count():
                # Check if mouse is actually over the slot (not in margin)
                slot_x = slot_index * (self.renderer.tower_slot_width + self.renderer.tower_slot_margin) - self.scroll_offset + 20
                if slot_x <= mouse_x <= slot_x + self.renderer.tower_slot_width:
                    self.hovered_tower_index = slot_index
                else:
                    self.hovered_tower_index = None
            else:
                self.hovered_tower_index = None
        else:
            self.hovered_tower_index = None
    
    def handle_tower_bar_click(self, pos: Tuple[int, int]) -> Optional[int]:
        """Handle clicks on the tower bar, return selected tower index"""
        mouse_x, mouse_y = pos
        
        if self.renderer.bottom_bar_y <= mouse_y <= self.screen_height:
            relative_x = mouse_x - 20 + self.scroll_offset
            slot_index = int(relative_x // (self.renderer.tower_slot_width + self.renderer.tower_slot_margin))
            
            if 0 <= slot_index < self.tower_data_manager.get_tower_count():
                slot_x = slot_index * (self.renderer.tower_slot_width + self.renderer.tower_slot_margin) - self.scroll_offset + 20
                if slot_x <= mouse_x <= slot_x + self.renderer.tower_slot_width:
                    self.selected_tower_index = slot_index
                    return slot_index
        
        return None
    
    def handle_scroll(self, direction: int):
        """Handle scrolling in the tower bar"""
        # Only scroll if mouse is over the tower bar
        mouse_x, mouse_y = self.mouse_pos
        if not (self.renderer.bottom_bar_y <= mouse_y <= self.screen_height):
            return
            
        # Use a smaller scroll speed that's more proportional to the scroll range
        scroll_speed = 30  # Reduced from 50 for smoother scrolling
        
        if direction > 0:  # Scroll right
            self.scroll_offset = min(self.max_scroll, self.scroll_offset + scroll_speed)
            # Recalculate max_scroll in case tower costs changed
            self.max_scroll = self._calculate_max_scroll()
        elif direction < 0:  # Scroll left  
            self.scroll_offset = max(0, self.scroll_offset - scroll_speed)
    
    def handle_tower_click(self, pos: Tuple[int, int], towers: List) -> bool:
        """Handle clicks on placed towers to show range"""
        mouse_x, mouse_y = pos
        
        # Don't handle if clicking in UI areas
        if mouse_y >= self.renderer.bottom_bar_y or mouse_y <= 130:
            return False
        
        # Check if click is within screen bounds
        if mouse_x < 0 or mouse_x > self.screen_width or mouse_y < 140 or mouse_y >= self.renderer.bottom_bar_y:
            return False
        
        for tower in towers:
            # Check if click is within tower's area
            distance = math.sqrt((mouse_x - tower.x)**2 + (mouse_y - tower.y)**2)
            if distance <= tower.size + 5:  # Small buffer for easier clicking
                self.selected_placed_tower = tower
                return True
        
        # Click elsewhere deselects tower
        self.selected_placed_tower = None
        return False
    
    def handle_speed_button_click(self, pos: Tuple[int, int]) -> bool:
        """Handle clicks on the speed button, return True if clicked"""
        return self.renderer.is_speed_button_clicked(pos)
    
    def handle_rock_removal_button_click(self, pos: Tuple[int, int]) -> bool:
        """Handle clicks on the rock removal button, return True if clicked"""
        if self.renderer.is_rock_removal_button_clicked(pos):
            self.toggle_rock_removal_mode()
            return True
        return False
    
    def handle_enemy_info_button_click(self, pos: Tuple[int, int]) -> bool:
        """Handle clicks on the enemy info button, return True if clicked"""
        if self.renderer.is_enemy_info_button_clicked(pos):
            self.enemy_lookup.toggle_lookup()
            return True
        return False
    
    def handle_upgrade_all_button_click(self, pos: Tuple[int, int]) -> bool:
        """Handle clicks on the upgrade all button, return True if clicked"""
        return self.renderer.is_upgrade_all_button_clicked(pos)
    
    def toggle_rock_removal_mode(self):
        """Toggle rock removal mode on/off"""
        self.rock_removal_mode = not self.rock_removal_mode
        # Clear tower selection when entering rock removal mode
        if self.rock_removal_mode:
            self.selected_tower_index = None
    
    def is_rock_removal_mode(self) -> bool:
        """Check if currently in rock removal mode"""
        return self.rock_removal_mode
    
    def exit_rock_removal_mode(self):
        """Exit rock removal mode"""
        self.rock_removal_mode = False
    
    def get_selected_tower_type(self) -> Optional[str]:
        """Get the currently selected tower type"""
        if self.selected_tower_index is not None:
            return self.tower_data_manager.get_tower_type_by_index(self.selected_tower_index)
        return None
    
    def clear_tower_selection(self):
        """Clear tower selection"""
        self.selected_tower_index = None
    
    def reset_ui_state(self):
        """Reset all UI state to initial conditions"""
        # Clear all selections
        self.selected_tower_index = None
        self.hovered_tower_index = None
        self.selected_placed_tower = None
        
        # Reset rock removal mode
        self.rock_removal_mode = False
        
        # Reset scroll position
        self.scroll_offset = 0
        
        # Reset mouse position
        self.mouse_pos = (0, 0)
        
        # Recalculate max scroll for clean state
        self.max_scroll = self._calculate_max_scroll()
    
    def handle_enemy_lookup_event(self, event) -> bool:
        """Handle events for the enemy lookup system"""
        return self.enemy_lookup.handle_event(event)
    
    def is_enemy_lookup_active(self) -> bool:
        """Check if the enemy lookup interface is currently active"""
        return self.enemy_lookup.is_lookup_active()
    
    def refresh_scroll_bounds(self):
        """Refresh scroll bounds - call when tower data changes"""
        old_max = self.max_scroll
        self.max_scroll = self._calculate_max_scroll()
        
        # Ensure scroll offset is still valid
        self.scroll_offset = min(self.scroll_offset, self.max_scroll)
    
    def draw_complete_ui(self, screen: pygame.Surface, game_state: Dict):
        """Draw the complete user interface"""
        # Refresh scroll bounds to ensure they're up to date
        self.refresh_scroll_bounds()
        
        # Main game stats (top area) with speed button and rock removal
        rock_removal_cost = game_state.get('rock_removal_cost', 25)
        self.renderer.draw_game_stats(screen, game_state['money'], game_state['lives'], 
                                    game_state['wave_info'], game_state.get('game_speed', 1),
                                    self.rock_removal_mode, rock_removal_cost)
        
        # Performance info (top right)
        if 'performance' in game_state:
            self.renderer.draw_performance_info(screen, game_state['performance'])
        
        # Don't draw normal UI if game is over or won
        if game_state.get('game_over', False):
            self.renderer.draw_game_over_screen(screen)
            return
        elif game_state.get('victory', False) or game_state.get('show_victory_screen', False):
            self.renderer.draw_victory_screen(screen, game_state['wave_info'])
            return
        
        # Bottom tower bar
        tower_data = self.tower_data_manager.get_all_tower_data()
        self.renderer.draw_tower_bar(screen, tower_data, game_state['money'], 
                                   self.scroll_offset, self.max_scroll, 
                                   self.selected_tower_index, self.hovered_tower_index)
        
        # Tower tooltip
        if self.hovered_tower_index is not None:
            tower_data = self.tower_data_manager.get_tower_data(
                self.tower_data_manager.get_tower_type_by_index(self.hovered_tower_index)
            )
            if tower_data:
                self.renderer.draw_tower_tooltip(screen, tower_data, self.mouse_pos)
        
        # Draw pause overlay if paused
        if game_state['paused']:
            self.renderer.draw_pause_overlay(screen)
        
        # Draw wave complete notification
        if game_state.get('show_wave_complete', False):
            wave_bonus = game_state.get('wave_bonus', 0)
            completed_wave = game_state.get('completed_wave_number', 1)
            self.renderer.draw_wave_complete(screen, completed_wave, wave_bonus)
        
        # Draw wave start indicator (manual wave start)
        if game_state.get('can_start_wave', False) and game_state.get('waiting_for_next_wave', False):
            current_wave = game_state['wave_info']['wave_number']
            next_wave_number = current_wave + 1
            self.renderer.draw_wave_start_indicator(screen, next_wave_number)
        
        # Draw enemy lookup interface
        self.enemy_lookup.draw(screen)
