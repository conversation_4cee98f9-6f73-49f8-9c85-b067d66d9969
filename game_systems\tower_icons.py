"""
Tower Icon System - Loads and manages pixel art icons for tower types
"""
import pygame
import os
from typing import Dict, Optional

class TowerIconManager:
    """Manages loading and caching of tower icon images"""
    
    def __init__(self, icon_size: int = 32):
        self.icon_size = icon_size
        self.icons: Dict[str, pygame.Surface] = {}
        self.icons_loaded = False
        
        # Tower type mapping (same as in tower_data.py)
        self.tower_types = [
            'basic', 'sniper', 'freezer', 'detector', 'antiair', 'poison', 
            'laser', 'cannon', 'lightning', 'flame', 'ice', 'explosive', 
            'missile', 'splash', 'destroyer'
        ]
        
        # Expected image filenames (you can provide these)
        self.icon_filenames = {
            'basic': 'basic_tower.png',
            'sniper': 'sniper_tower.png', 
            'freezer': 'freezer_tower.png',
            'detector': 'detector_tower.png',
            'antiair': 'antiair_tower.png',
            'poison': 'poison_tower.png',
            'laser': 'laser_tower.png',
            'cannon': 'cannon_tower.png',  # Also supports 'canon_tower.png' (typo)
            'lightning': 'lightning_tower.png',
            'flame': 'flame_tower.png',
            'ice': 'ice_tower.png',
            'explosive': 'explosive_tower.png',
            'missile': 'missile_tower.png',
            'splash': 'splash_tower.png',
            'destroyer': 'destroyer_tower.png'
        }
    
    def load_icons(self, icons_directory: str = "assets/tower_icons"):
        """Load all tower icons from the specified directory"""
        if self.icons_loaded:
            return
            
        # Create icons directory if it doesn't exist
        if not os.path.exists(icons_directory):
            os.makedirs(icons_directory)
            print(f"Created icons directory: {icons_directory}")
            print("Please place your tower icon images in this directory.")
            print("Expected filenames:")
            for tower_type, filename in self.icon_filenames.items():
                print(f"  {tower_type}: {filename}")
            return
        
        # Load each tower icon
        for tower_type, filename in self.icon_filenames.items():
            icon_path = os.path.join(icons_directory, filename)
            
            # Try different file extensions and variations
            extensions = ['.png', '.jpg', '.jpeg']
            icon_surface = None
            
            for ext in extensions:
                # Try normal extension
                test_path = icon_path.replace('.png', ext)
                if os.path.exists(test_path):
                    try:
                        icon_surface = pygame.image.load(test_path)
                        # Scale to desired size
                        icon_surface = pygame.transform.scale(icon_surface, (self.icon_size, self.icon_size))
                        self.icons[tower_type] = icon_surface
                        print(f"Loaded icon for {tower_type}: {test_path}")
                        break
                    except pygame.error as e:
                        print(f"Failed to load {test_path}: {e}")
                
                # Try double extension (like .png.png)
                double_ext_path = test_path + ext
                if os.path.exists(double_ext_path):
                    try:
                        icon_surface = pygame.image.load(double_ext_path)
                        # Scale to desired size
                        icon_surface = pygame.transform.scale(icon_surface, (self.icon_size, self.icon_size))
                        self.icons[tower_type] = icon_surface
                        print(f"Loaded icon for {tower_type}: {double_ext_path}")
                        break
                    except pygame.error as e:
                        print(f"Failed to load {double_ext_path}: {e}")
            
            # Special case for cannon tower typo
            if tower_type == 'cannon' and icon_surface is None:
                canon_path = os.path.join(icons_directory, 'canon_tower.png')
                for ext in extensions:
                    test_canon_path = canon_path.replace('.png', ext)
                    if os.path.exists(test_canon_path):
                        try:
                            icon_surface = pygame.image.load(test_canon_path)
                            icon_surface = pygame.transform.scale(icon_surface, (self.icon_size, self.icon_size))
                            self.icons[tower_type] = icon_surface
                            print(f"Loaded icon for {tower_type} (typo): {test_canon_path}")
                            break
                        except pygame.error as e:
                            print(f"Failed to load {test_canon_path}: {e}")
                    
                    # Try double extension for typo
                    double_canon_path = test_canon_path + ext
                    if os.path.exists(double_canon_path):
                        try:
                            icon_surface = pygame.image.load(double_canon_path)
                            icon_surface = pygame.transform.scale(icon_surface, (self.icon_size, self.icon_size))
                            self.icons[tower_type] = icon_surface
                            print(f"Loaded icon for {tower_type} (typo): {double_canon_path}")
                            break
                        except pygame.error as e:
                            print(f"Failed to load {double_canon_path}: {e}")
            
            if icon_surface is None:
                print(f"Warning: No icon found for {tower_type} (tried: {filename})")
                # Create a fallback colored square
                fallback_surface = pygame.Surface((self.icon_size, self.icon_size))
                fallback_surface.fill((128, 128, 128))  # Gray
                self.icons[tower_type] = fallback_surface
        
        self.icons_loaded = True
        print(f"Loaded {len(self.icons)} tower icons")
    
    def get_icon(self, tower_type: str) -> Optional[pygame.Surface]:
        """Get the icon for a specific tower type"""
        if not self.icons_loaded:
            self.load_icons()
        
        return self.icons.get(tower_type)
    
    def get_icon_for_ui(self, tower_type: str, can_afford: bool = True) -> Optional[pygame.Surface]:
        """Get the icon for UI display, with affordability tinting"""
        icon = self.get_icon(tower_type)
        if icon is None:
            return None
        
        if can_afford:
            return icon
        else:
            # Create a grayed-out version for unaffordable towers
            grayed_icon = icon.copy()
            grayed_icon.fill((128, 128, 128), special_flags=pygame.BLEND_MULT)
            return grayed_icon
    
    def has_icons_loaded(self) -> bool:
        """Check if any icons have been successfully loaded"""
        return self.icons_loaded and len(self.icons) > 0
    
    def get_loaded_icon_count(self) -> int:
        """Get the number of successfully loaded icons"""
        return len(self.icons) 