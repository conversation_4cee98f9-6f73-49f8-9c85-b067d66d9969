from .tower import Tower
import pygame
import math

class DestroyerTower(Tower):
    """Navy destroyer (3x3 size) that requires at least 3 water blocks and no path/stone blocks, with extremely long range AOE missiles"""
    
    def __init__(self, x, y):
        super().__init__(x, y, 'destroyer')
        self.damage = 80  # Very high damage for naval artillery
        self.range = 500  # Extremely long range - naval artillery
        self.fire_rate = 150  # Slower firing (2.5 seconds at 60 FPS)
        self.projectile_speed = 6.0  # Fast naval missiles
        self.size = 25  # Larger size for 3x3 tower
        self.color = (70, 130, 180)  # Steel blue
        
        # Naval missile properties - enhanced AOE
        self.explosion_radius = 80  # Massive explosion radius
        self.explosion_damage = 40  # High splash damage
        self.missile_count = 1  # Single powerful missile
        
        # Can target both ground and flying enemies
        self.can_target_flying = True
        self.can_target_invisible = False
        
        # Water-only placement restriction
        self.terrain_restrictions = [3]  # Can only be placed on water (terrain type 3)
        
        # Naval charging system
        self.charging = False
        self.charge_timer = 0
        self.charge_duration = 30  # 0.5 second charge time (balanced for gameplay)
        
        # Visual effects
        self.radar_rotation = 0
        self.muzzle_flash_timer = 0
        
        # Store base values for custom attributes
        self.base_explosion_radius = self.explosion_radius
        self.base_explosion_damage = self.explosion_damage
        self.base_charge_duration = self.charge_duration
        
        # CRITICAL: Finalize initialization to set base_range correctly
        self.finalize_initialization()
        
    def reset_stats_to_base(self):
        """Reset destroyer stats to base values, including custom naval attributes"""
        # Call parent method for basic stats
        super().reset_stats_to_base()
        
        # Reset custom naval attributes
        self.explosion_radius = self.base_explosion_radius
        self.explosion_damage = self.base_explosion_damage
        self.charge_duration = self.base_charge_duration
    

    
    @staticmethod
    def get_static_stats():
        """Get tower stats without creating an instance - for UI display"""
        return {
            'damage': 80,
            'range': 500,
            'fire_rate': 150,
            'explosion_radius': 80,
            'explosion_damage': 40,
            'charge_duration': 30,
            'placement': '3x3 size, needs 3+ water blocks, no path/stone',
            'special': 'Charges before firing massive AOE missiles'
        }
        
    def can_target_enemy(self, enemy):
        """Check if this tower can target a specific enemy"""
        # First check parent class immunity
        if not super().can_target_enemy(enemy):
            return False
        
        # Check for invisible enemies - destroyer can only target detected invisible enemies
        if hasattr(enemy, 'invisible') and enemy.invisible:
            if not self.can_target_invisible:
                # Check if enemy has been detected by a detector tower
                if not (hasattr(enemy, 'detected_by_detector') and enemy.detected_by_detector):
                    return False
        
        # Check flying vs ground targeting
        if hasattr(enemy, 'flying') and enemy.flying:
            # This is a flying enemy - destroyer can target flying enemies
            if not self.can_target_flying:
                return False
        
        # Allow targeting of spectral and crystalline enemies if detected (immunity handled in take_damage)
        # For spectral: must be detected by detector
        enemy_class_name = type(enemy).__name__
        if enemy_class_name == 'SpectralEnemy':
            if not (hasattr(enemy, 'detected_by_detector') and enemy.detected_by_detector):
                return False
        # For crystalline: no special targeting restriction, immunity handled in take_damage
        
        return True
    
    def acquire_target(self, enemies):
        """Find target - prioritize enemy closest to end of path"""
        valid_targets = []
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            can_target = self.can_target_enemy(enemy)
            if distance <= self.range and can_target:
                valid_targets.append((enemy, distance))
        if valid_targets:
            # Target enemy closest to end of path
            self.target = max(valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
            # Calculate angle to target
            if self.target:
                dx = self.target.x - self.x
                dy = self.target.y - self.y
                self.angle = math.atan2(dy, dx)
        else:
            self.target = None
    
    def update(self, enemies, projectiles):
        """Update destroyer with naval firing mechanism"""
        # Update visual effects
        self.radar_rotation += 3
        if self.muzzle_flash_timer > 0:
            self.muzzle_flash_timer -= 1
        
        # Update fire timer
        if self.fire_timer > 0:
            self.fire_timer -= 1
        
        # Update charging
        if self.charging:
            self.charge_timer += 1
            if self.charge_timer >= self.charge_duration:
                # Fire naval missile
                self.fire_naval_missile(projectiles)
                self.charging = False
                self.charge_timer = 0
                self.fire_timer = self.fire_rate
                self.muzzle_flash_timer = 15  # Visual effect
        
        # Find and acquire target
        self.acquire_target(enemies)
        
        # Start charging if ready and have target
        if self.target and self.fire_timer <= 0 and not self.charging:
            self.charging = True
            self.charge_timer = 0
    
    def update_with_speed(self, enemies, projectiles, speed_multiplier: float):
        """Update destroyer with speed multiplier"""

        
        # Update visual effects
        self.radar_rotation += 3 * speed_multiplier
        if self.muzzle_flash_timer > 0:
            self.muzzle_flash_timer -= speed_multiplier
        
        # Update fire timer with speed multiplier
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier
        
        # Update charging with speed multiplier
        if self.charging:
            self.charge_timer += speed_multiplier
            if self.charge_timer >= self.charge_duration:
                # Fire naval missile
                self.fire_naval_missile(projectiles)
                self.charging = False
                self.charge_timer = 0
                self.fire_timer = self.fire_rate
                self.muzzle_flash_timer = 15
        
        # Find and acquire target
        self.acquire_target(enemies)
        
        # Start charging if ready and have target
        if self.target and self.fire_timer <= 0 and not self.charging:
            self.charging = True
            self.charge_timer = 0
    
    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Optimized update for destroyer - same as regular update_with_speed"""

        
        # For destroyer, optimized version is the same as regular version
        # Just call the regular update_with_speed method
        self.update_with_speed(enemies, projectiles, speed_multiplier)
    
    def shoot(self, projectiles):
        """This is called by the base class but we handle firing in update()"""
        pass
    
    def fire_naval_missile(self, projectiles):
        """Fire powerful naval missile"""
        if not self.target:
            return
            
        # Launch position from the destroyer's gun
        launch_x = self.x + math.cos(self.angle) * 15  # type: ignore
        launch_y = self.y + math.sin(self.angle) * 15  # type: ignore
        
        missile = NavalMissile(
            launch_x, launch_y, self.target.x, self.target.y,
            self.projectile_speed, self.damage, self.explosion_radius, self.explosion_damage
        )
        missile.source_tower_id = self.tower_id
        projectiles.append(missile)
        
        # Generate currency immediately when firing
        self.generate_firing_currency()
    
    def draw(self, screen, selected: bool = False):
        """Draw navy destroyer with sprite support and naval aesthetic"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)
        
        # Draw range circle only when selected
        if selected:
            pygame.draw.circle(screen, (100, 149, 237), (int(self.x), int(self.y)), int(self.range), 2)
        
        # Draw water splash effect around destroyer
        for i in range(6):
            splash_angle = (i * 60) * math.pi / 180
            splash_x = self.x + math.cos(splash_angle) * (self.size + 5)
            splash_y = self.y + math.sin(splash_angle) * (self.size + 5)
            pygame.draw.circle(screen, (135, 206, 235), (int(splash_x), int(splash_y)), 3)
        
        # Draw charging effect (naval targeting system)
        if self.charging:
            charge_progress = self.charge_timer / self.charge_duration
            charge_radius = int(25 + 15 * charge_progress)
            charge_color = (255, 255, int(100 + 155 * charge_progress))
            
            pygame.draw.circle(screen, charge_color, (int(self.x), int(self.y)), charge_radius, 3)
            
            # Draw targeting lines
            for i in range(int(charge_progress * 8)):
                target_angle = (i * 45 + self.radar_rotation) * math.pi / 180
                target_x = self.x + math.cos(target_angle) * charge_radius
                target_y = self.y + math.sin(target_angle) * charge_radius
                pygame.draw.line(screen, (255, 255, 0), (int(self.x), int(self.y)), 
                               (int(target_x), int(target_y)), 2)
        
        # Draw rotating radar
        radar_angle = self.radar_rotation * math.pi / 180
        radar_end_x = self.x + math.cos(radar_angle) * 12
        radar_end_y = self.y + math.sin(radar_angle) * 12
        pygame.draw.line(screen, (0, 255, 0), (int(self.x), int(self.y)), 
                       (int(radar_end_x), int(radar_end_y)), 2)
        
        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)
                
                # Draw muzzle flash when firing (on top of sprite)
                if self.target and self.muzzle_flash_timer > 0:
                    flash_intensity = self.muzzle_flash_timer / 15
                    flash_color = (255, int(200 * flash_intensity), 0)
                    # Position flash at sprite center since we can't calculate turret position
                    pygame.draw.circle(screen, flash_color, (int(self.x), int(self.y)), 8)
                
                # Draw upgrade indicator if available
                self.draw_upgrade_indicator(screen)
                return
        
        # Fallback to custom drawing
        # Draw main destroyer hull
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (25, 25, 112), (int(self.x), int(self.y)), self.size, 3)
        
        # Draw destroyer superstructure
        superstructure_rect = pygame.Rect(self.x - 10, self.y - 12, 20, 8)
        pygame.draw.rect(screen, (105, 105, 105), superstructure_rect)
        pygame.draw.rect(screen, (25, 25, 112), superstructure_rect, 2)
        
        # Draw naval gun turret
        turret_x = self.x + math.cos(self.angle) * 8
        turret_y = self.y + math.sin(self.angle) * 8
        pygame.draw.circle(screen, (105, 105, 105), (int(turret_x), int(turret_y)), 6)
        pygame.draw.circle(screen, (25, 25, 112), (int(turret_x), int(turret_y)), 6, 2)
        
        # Draw gun barrel pointing at target
        if self.target:
            barrel_length = 20
            barrel_end_x = turret_x + math.cos(self.angle) * barrel_length
            barrel_end_y = turret_y + math.sin(self.angle) * barrel_length
            pygame.draw.line(screen, (64, 64, 64), (int(turret_x), int(turret_y)), 
                           (int(barrel_end_x), int(barrel_end_y)), 4)
            
            # Draw muzzle flash when firing
            if self.muzzle_flash_timer > 0:
                flash_intensity = self.muzzle_flash_timer / 15
                flash_color = (255, int(200 * flash_intensity), 0)
                pygame.draw.circle(screen, flash_color, (int(barrel_end_x), int(barrel_end_y)), 8)
        
        # Draw naval identification
        pygame.draw.circle(screen, (255, 255, 255), (int(self.x), int(self.y + 20)), 3)
        
        # Draw upgrade indicator if available
        self.draw_upgrade_indicator(screen)


class NavalMissile:
    """Naval missile projectile with massive AOE damage"""
    
    def __init__(self, x, y, target_x, target_y, speed, damage, explosion_radius, explosion_damage):
        self.x = x
        self.y = y
        self.target_x = target_x
        self.target_y = target_y
        self.speed = speed
        self.damage = damage
        self.explosion_radius = explosion_radius
        self.explosion_damage = explosion_damage
        self.color = (70, 130, 180)  # Steel blue missile
        self.size = 5  # Larger missile
        self.tower_type = 'destroyer'  # For screenshake detection
        
        # Enhanced homing properties for naval missiles
        self.homing_strength = 0.2  # Strong homing for naval precision
        
        # Calculate initial direction
        dx = target_x - x
        dy = target_y - y
        distance = math.sqrt(dx**2 + dy**2)
        if distance > 0:
            self.dx = (dx / distance) * speed
            self.dy = (dy / distance) * speed
        else:
            self.dx = self.dy = 0
        
        self.active = True
        self.should_remove = False
        self.trail_positions = []  # For visual trail
        
        # Enhanced explosion animation
        self.exploding = False
        self.explosion_timer = 0
        self.explosion_duration = 20  # Longer explosion for naval missiles
        self.explosion_particles = []
        
        # Counter for update calls
        self.update_count = 0
    
    def update(self, enemies=None):
        """Update naval missile position with homing"""
        if self.exploding:
            # Update explosion animation
            self.explosion_timer += 1
            if self.explosion_timer >= self.explosion_duration:
                self.should_remove = True
            return
            
        if not self.active:
            return
        
        # Find closest enemy to home towards
        if enemies:
            closest_enemy = None
            closest_distance = float('inf')
            
            for enemy in enemies:
                distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
                if distance < closest_distance:
                    closest_distance = distance
                    closest_enemy = enemy
            
            # Update target if we found a closer enemy
            if closest_enemy:
                self.target_x = closest_enemy.x
                self.target_y = closest_enemy.y
            
            # Calculate direction to target
            target_dx = self.target_x - self.x
            target_dy = self.target_y - self.y
            target_distance = math.sqrt(target_dx**2 + target_dy**2)
            
            if target_distance > 0:
                # Normalize target direction
                target_dx /= target_distance
                target_dy /= target_distance
                
                # Apply homing
                self.dx += target_dx * self.homing_strength
                self.dy += target_dy * self.homing_strength
                
                # Normalize velocity to maintain speed
                current_speed = math.sqrt(self.dx**2 + self.dy**2)
                if current_speed > 0:
                    self.dx = (self.dx / current_speed) * self.speed
                    self.dy = (self.dy / current_speed) * self.speed
        
        # Update position
        self.x += self.dx
        self.y += self.dy
        
        # Add to trail
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 10:
            self.trail_positions.pop(0)
        
        # Check if reached target or off screen
        target_distance = math.sqrt((self.x - self.target_x)**2 + (self.y - self.target_y)**2)
        if target_distance < 15 or self.x < 0 or self.x > 1200 or self.y < 0 or self.y > 800:
            self.explode(enemies)
    
    def update_with_speed(self, enemies, speed_multiplier: float):
        """Update naval missile with speed multiplier"""
        self.update_count += 1
        
        if self.exploding:
            self.explosion_timer += speed_multiplier
            if self.explosion_timer >= self.explosion_duration:
                self.should_remove = True
            return
            
        if not self.active:
            return
        
        # Find closest enemy to home towards
        if enemies:
            closest_enemy = None
            closest_distance = float('inf')
            
            for enemy in enemies:
                distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
                if distance < closest_distance:
                    closest_distance = distance
                    closest_enemy = enemy
            
            # Update target if we found a closer enemy
            if closest_enemy:
                self.target_x = closest_enemy.x
                self.target_y = closest_enemy.y
            
            # Calculate direction to target
            target_dx = self.target_x - self.x
            target_dy = self.target_y - self.y
            target_distance = math.sqrt(target_dx**2 + target_dy**2)
            
            if target_distance > 0:
                # Normalize target direction
                target_dx /= target_distance
                target_dy /= target_distance
                
                # Apply homing with speed multiplier
                self.dx += target_dx * self.homing_strength * speed_multiplier
                self.dy += target_dy * self.homing_strength * speed_multiplier
                
                # Normalize velocity to maintain speed
                current_speed = math.sqrt(self.dx**2 + self.dy**2)
                if current_speed > 0:
                    self.dx = (self.dx / current_speed) * self.speed
                    self.dy = (self.dy / current_speed) * self.speed
        
        # Update position with speed multiplier
        self.x += self.dx * speed_multiplier
        self.y += self.dy * speed_multiplier
        
        # Add to trail
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 10:
            self.trail_positions.pop(0)
        
        # Check if reached target or off screen
        target_distance = math.sqrt((self.x - self.target_x)**2 + (self.y - self.target_y)**2)
        if target_distance < 15 or self.x < 0 or self.x > 1200 or self.y < 0 or self.y > 800:
            self.explode(enemies)
    
    def on_impact(self):
        """Handle missile impact - trigger explosion"""
        self.exploding = True
        self.active = False
        self.explosion_timer = 0
        self.create_explosion_particles()
    
    def explode(self, enemies):
        """Create massive naval explosion and damage nearby enemies"""
        total_damage = 0
        
        # Damage all enemies in explosion radius with enhanced naval damage
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.explosion_radius:
                # Direct hit gets full damage, splash gets high damage (naval artillery)
                if distance < 20:  # Direct hit
                    damage_dealt = enemy.take_damage(self.damage, 'destroyer')
                else:  # Splash damage
                    damage_dealt = enemy.take_damage(self.explosion_damage, 'destroyer')
                total_damage += damage_dealt
        
        # Start explosion animation
        self.active = False
        self.exploding = True
        self.explosion_timer = 0
        self.create_explosion_particles()
        
        # No longer trigger screenshake for naval explosions - only explosive tower can cause screenshake
        # self.should_trigger_screenshake = True
        
        return total_damage
    
    def draw(self, screen):
        """Draw naval missile with enhanced trail"""
        if self.exploding:
            # Draw massive explosion animation
            self.draw_explosion(screen)
            return
            
        if not self.active:
            return
            
        # Draw enhanced trail
        for i, (trail_x, trail_y) in enumerate(self.trail_positions):
            alpha = (i + 1) / len(self.trail_positions)
            trail_color = (int(70 * alpha), int(130 * alpha), int(180 * alpha))
            pygame.draw.circle(screen, trail_color, (int(trail_x), int(trail_y)), max(1, int(4 * alpha)))
        
        # Draw naval missile body
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), 5)
        pygame.draw.circle(screen, (255, 255, 255), (int(self.x), int(self.y)), 3)
        pygame.draw.circle(screen, (255, 0, 0), (int(self.x), int(self.y)), 1)
        
        # Draw enhanced flame trail
        if len(self.trail_positions) > 1:
            prev_x, prev_y = self.trail_positions[-1]
            flame_x = prev_x - self.dx * 0.7
            flame_y = prev_y - self.dy * 0.7
            pygame.draw.circle(screen, (255, 100, 0), (int(flame_x), int(flame_y)), 3)
    
    def draw_explosion(self, screen):
        """Draw massive naval explosion animation"""
        # Update and draw explosion particles
        for particle in self.explosion_particles[:]:
            # Update particle position
            particle['x'] += particle['dx']
            particle['y'] += particle['dy']
            particle['life'] -= 1
            
            # Apply gravity and friction
            particle['dy'] += 0.15
            particle['dx'] *= 0.99
            
            if particle['life'] <= 0:
                self.explosion_particles.remove(particle)
                continue
            
            # Calculate alpha based on life remaining
            alpha = particle['life'] / particle['max_life']
            size = int(particle['size'] * alpha)
            
            if size > 0:
                # Draw particle with fading effect
                color = particle['color']
                faded_color = (int(color[0] * alpha), int(color[1] * alpha), int(color[2] * alpha))
                pygame.draw.circle(screen, faded_color, (int(particle['x']), int(particle['y'])), size)
        
        # Draw massive explosion shockwave
        progress = self.explosion_timer / self.explosion_duration
        shockwave_radius = int(self.explosion_radius * progress)
        shockwave_alpha = int(255 * (1 - progress))
        
        if shockwave_alpha > 0 and shockwave_radius > 0:
            # Multiple shockwave rings for naval explosion
            for ring in range(3):
                ring_radius = shockwave_radius - (ring * 10)
                if ring_radius > 0:
                    ring_alpha = max(0, shockwave_alpha - (ring * 80))
                    shockwave_color = (255, 100, 0, ring_alpha)
                    pygame.draw.circle(screen, shockwave_color[:3], (int(self.x), int(self.y)), ring_radius, 4)
    
    def create_explosion_particles(self):
        """Create enhanced explosion particle effects for naval missiles"""
        import random
        for _ in range(30):  # More particles for naval explosion
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(3, 12)
            particle = {
                'x': self.x,
                'y': self.y,
                'dx': math.cos(angle) * speed,
                'dy': math.sin(angle) * speed,
                'life': random.randint(10, 20),
                'max_life': 20,
                'size': random.randint(3, 8),
                'color': random.choice([(255, 100, 0), (255, 150, 0), (255, 200, 0), (255, 255, 0), (70, 130, 180)])
            }
            self.explosion_particles.append(particle)
    
    def check_collision(self, enemies):
        """Check collision with enemies and handle explosion"""
        if not self.active or self.exploding:
            return {'hit': False, 'damage': 0, 'tower_id': None}
            
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= (self.size + getattr(enemy, 'size', 10)):  # Close enough for collision
                # Trigger explosion
                total_damage = self.explode(enemies)
                
                return {'hit': True, 'damage': total_damage, 'tower_id': getattr(self, 'source_tower_id', None)}
        
        return {'hit': False, 'damage': 0, 'tower_id': None}
