{"game_config": {"starting_money": 29, "starting_lives": 17}, "progression_config": {"starting_money": 200, "starting_lives": 17, "economic_scaling": "automatic"}, "tower_config": {"cost_progression": {"early_game_waves": 15, "mid_game_waves": 30, "early_increase_per_wave": 0.02, "mid_increase_per_wave": 0.045, "late_increase_per_wave": 0.1, "max_cost_multiplier": 4.5}, "dynamic_cost_increase": {"per_tower_built_multiplier": 0.15, "max_per_tower_multiplier": 20}}, "enemy_buffs": {"max_health_modifier": 0.8, "max_speed_modifier": 0.9, "global_damage_modifier": 1.0, "special_abilities_enabled": true}, "wave_config": {"total_waves": 48, "spawn_config": {"base_enemy_count": 11, "base_spawn_delay": 111, "min_spawn_delay": 24.0, "boss_enemy_count": 1}, "round_progression": {"enemy_increase_per_round": {"wave_ranges": {"1-5": 1, "6-10": 2, "11-15": 3, "16-20": 4, "21-25": 5, "26-30": 6, "31-35": 7, "36-40": 8, "41-45": 9, "46-50": 10, "51-55": 11, "56-60": 12, "61-65": 13, "66-70": 14, "71-75": 15, "76-80": 16}, "default": 1}, "spawn_delay_reduction_per_round": {"wave_ranges": {"1-10": 4, "11-20": 6, "21-30": 8, "31-40": 9, "41-50": 12, "51-60": 14, "61-70": 16, "71-80": 17}, "default": 4}, "special_rounds": {"10": {"enemy_multiplier": 2.0, "spawn_delay_multiplier": 0.7000000000000001}, "20": {"enemy_multiplier": 2.5, "spawn_delay_multiplier": 0.6000000000000001}, "30": {"enemy_multiplier": 3.0, "spawn_delay_multiplier": 0.5}, "40": {"enemy_multiplier": 3.5, "spawn_delay_multiplier": 0.4}, "50": {"enemy_multiplier": 4.0, "spawn_delay_multiplier": 0.30000000000000004}, "60": {"enemy_multiplier": 4.5, "spawn_delay_multiplier": 0.2}, "70": {"enemy_multiplier": 5.0, "spawn_delay_multiplier": 0.2}, "80": {"enemy_multiplier": 5.5, "spawn_delay_multiplier": 0.2}}}, "wave_compositions": {"1-5": [["BasicEnemy", 0.5], ["FastEnemy", 0.2]], "6-10": [["BasicEnemy", 0.06666666666666668], ["TankEnemy", 0.06666666666666668], ["FlyingEnemy", 0.06666666666666668], ["ArmoredEnemy", 0.06666666666666668], ["FastEnemy", 0.06666666666666668], ["EnergyShieldEnemy", 0.06666666666666668], ["ToxicEnemy", 0.06666666666666668], ["FireElementalEnemy", 0.06666666666666668], ["PhaseShiftEnemy", 0.06666666666666668], ["VoidEnemy", 0.06666666666666668], ["TeleportingEnemy", 0.06666666666666668], ["SplittingEnemy", 0.06666666666666668], ["SpectralEnemy", 0.06666666666666668], ["CrystallineEnemy", 0.06666666666666668], ["AdaptiveEnemy", 0.06666666666666668]], "11-15": [["BasicEnemy", 0.06666666666666668], ["TankEnemy", 0.06666666666666668], ["FlyingEnemy", 0.06666666666666668], ["ArmoredEnemy", 0.06666666666666668], ["FastEnemy", 0.06666666666666668], ["EnergyShieldEnemy", 0.06666666666666668], ["ToxicEnemy", 0.06666666666666668], ["FireElementalEnemy", 0.06666666666666668], ["PhaseShiftEnemy", 0.06666666666666668], ["VoidEnemy", 0.06666666666666668], ["TeleportingEnemy", 0.06666666666666668], ["SplittingEnemy", 0.06666666666666668], ["SpectralEnemy", 0.06666666666666668], ["CrystallineEnemy", 0.06666666666666668], ["AdaptiveEnemy", 0.06666666666666668]], "16-20": [["BasicEnemy", 0.04666666666666667], ["TankEnemy", 0.04666666666666667], ["FlyingEnemy", 0.04666666666666667], ["ArmoredEnemy", 0.04666666666666667], ["FastEnemy", 0.04666666666666667], ["EnergyShieldEnemy", 0.04666666666666667], ["ToxicEnemy", 0.04666666666666667], ["FireElementalEnemy", 0.04666666666666667], ["PhaseShiftEnemy", 0.04666666666666667], ["VoidEnemy", 0.04666666666666667], ["TeleportingEnemy", 0.04666666666666667], ["SplittingEnemy", 0.04666666666666667], ["SpectralEnemy", 0.04666666666666667], ["CrystallineEnemy", 0.04666666666666667], ["AdaptiveEnemy", 0.04666666666666667], ["ShieldedEnemy", 0.30000000000000004]], "21-30": [["BasicEnemy", 0.046666666666666676], ["TankEnemy", 0.046666666666666676], ["FlyingEnemy", 0.046666666666666676], ["ArmoredEnemy", 0.046666666666666676], ["FastEnemy", 0.046666666666666676], ["EnergyShieldEnemy", 0.046666666666666676], ["ToxicEnemy", 0.046666666666666676], ["FireElementalEnemy", 0.046666666666666676], ["PhaseShiftEnemy", 0.046666666666666676], ["VoidEnemy", 0.046666666666666676], ["TeleportingEnemy", 0.046666666666666676], ["SplittingEnemy", 0.046666666666666676], ["SpectralEnemy", 0.046666666666666676], ["CrystallineEnemy", 0.046666666666666676], ["AdaptiveEnemy", 0.046666666666666676], ["ShieldedEnemy", 0.10000000000000002], ["InvisibleEnemy", 0.10000000000000002], ["GroundedEnemy", 0.10000000000000002]], "31-40": [["BasicEnemy", 0.042253521126760556], ["TankEnemy", 0.042253521126760556], ["FlyingEnemy", 0.042253521126760556], ["ArmoredEnemy", 0.042253521126760556], ["FastEnemy", 0.042253521126760556], ["EnergyShieldEnemy", 0.042253521126760556], ["ToxicEnemy", 0.042253521126760556], ["FireElementalEnemy", 0.042253521126760556], ["PhaseShiftEnemy", 0.042253521126760556], ["VoidEnemy", 0.042253521126760556], ["TeleportingEnemy", 0.042253521126760556], ["SplittingEnemy", 0.042253521126760556], ["SpectralEnemy", 0.042253521126760556], ["CrystallineEnemy", 0.042253521126760556], ["AdaptiveEnemy", 0.042253521126760556], ["ShieldedEnemy", 0.09389671361502344], ["InvisibleEnemy", 0.09389671361502344], ["GroundedEnemy", 0.09389671361502344], ["RegeneratingEnemy", 0.0845070422535211]], "41-50": [["BasicEnemy", 0.03896103896103895], ["TankEnemy", 0.03896103896103895], ["FlyingEnemy", 0.03896103896103895], ["ArmoredEnemy", 0.03896103896103895], ["FastEnemy", 0.03896103896103895], ["EnergyShieldEnemy", 0.03896103896103895], ["ToxicEnemy", 0.03896103896103895], ["FireElementalEnemy", 0.03896103896103895], ["PhaseShiftEnemy", 0.03896103896103895], ["VoidEnemy", 0.03896103896103895], ["TeleportingEnemy", 0.03896103896103895], ["SplittingEnemy", 0.03896103896103895], ["SpectralEnemy", 0.03896103896103895], ["CrystallineEnemy", 0.03896103896103895], ["AdaptiveEnemy", 0.03896103896103895], ["ShieldedEnemy", 0.08658008658008655], ["InvisibleEnemy", 0.08658008658008655], ["GroundedEnemy", 0.08658008658008655], ["RegeneratingEnemy", 0.07792207792207789], ["BlastProofEnemy", 0.07792207792207789]], "51-60": [["BasicEnemy", 0.03896103896103895], ["TankEnemy", 0.03896103896103895], ["FlyingEnemy", 0.03896103896103895], ["ArmoredEnemy", 0.03896103896103895], ["FastEnemy", 0.03896103896103895], ["EnergyShieldEnemy", 0.03896103896103895], ["ToxicEnemy", 0.03896103896103895], ["FireElementalEnemy", 0.03896103896103895], ["PhaseShiftEnemy", 0.03896103896103895], ["VoidEnemy", 0.03896103896103895], ["TeleportingEnemy", 0.03896103896103895], ["SplittingEnemy", 0.03896103896103895], ["SpectralEnemy", 0.03896103896103895], ["CrystallineEnemy", 0.03896103896103895], ["AdaptiveEnemy", 0.03896103896103895], ["ShieldedEnemy", 0.08658008658008655], ["InvisibleEnemy", 0.08658008658008655], ["GroundedEnemy", 0.08658008658008655], ["RegeneratingEnemy", 0.07792207792207789], ["BlastProofEnemy", 0.07792207792207789]], "61-80": [["BasicEnemy", 0.041198501872659166], ["TankEnemy", 0.041198501872659166], ["FlyingEnemy", 0.041198501872659166], ["ArmoredEnemy", 0.041198501872659166], ["FastEnemy", 0.041198501872659166], ["EnergyShieldEnemy", 0.041198501872659166], ["ToxicEnemy", 0.041198501872659166], ["FireElementalEnemy", 0.041198501872659166], ["PhaseShiftEnemy", 0.041198501872659166], ["VoidEnemy", 0.041198501872659166], ["TeleportingEnemy", 0.041198501872659166], ["SplittingEnemy", 0.041198501872659166], ["SpectralEnemy", 0.041198501872659166], ["CrystallineEnemy", 0.041198501872659166], ["AdaptiveEnemy", 0.041198501872659166], ["ShieldedEnemy", 0.09363295880149809], ["InvisibleEnemy", 0.09363295880149809], ["GroundedEnemy", 0.09363295880149809], ["RegeneratingEnemy", 0.03370786516853932], ["BlastProofEnemy", 0.03370786516853932], ["ToxicMutantEnemy", 0.03370786516853932]]}, "boss_waves": {"20": "TimeLordBoss", "25": "ShadowKing", "30": "NecromancerBoss", "35": "CrystalOverlord", "40": "TimeLordBoss", "45": "ShadowKing", "50": "NecromancerBoss", "55": "CrystalOverlord", "60": "TimeLordBoss", "65": "ShadowKing", "70": "NecromancerBoss", "75": "CrystalOverlord", "80": "TimeLordBoss"}, "enemy_scaling": {"health_per_wave": 0.31200000000000006, "speed_per_wave": 0.0585, "reward_per_wave": 0.1296, "size_per_wave": 0.018000000000000002, "max_health_multiplier": 48.0, "max_speed_multiplier": 4.0, "max_reward_multiplier": 17.6, "max_size_multiplier": 1.8, "damage_scaling_per_wave": 0.09000000000000001, "max_damage_multiplier": 3.2}, "money_config": {"normal_wave_bonus": 65, "boss_wave_bonus": 250}}, "map_config": {"default_map": {"width": 20, "height": 15, "terrain": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 4, 2, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 2, 0, 0, 0, 3, 1, 0, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 2], [2, 2, 0, 0, 2, 0, 1, 0, 1, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 2, 1, 0, 0, 0, 1, 0, 3, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 0, 0, 1, 3, 0, 0, 0, 0, 1, 0, 3, 0, 1, 1, 2, 0, 0, 2], [2, 1, 1, 1, 0, 0, 0, 0, 0, 0, 3, 1, 0, 1, 0, 0, 1, 0, 1, 2], [2, 0, 1, 1, 0, 0, 0, 0, 1, 2, 0, 0, 1, 0, 0, 2, 0, 1, 0, 2], [2, 0, 0, 1, 1, 0, 0, 1, 0, 1, 4, 1, 0, 1, 2, 3, 0, 0, 0, 2], [2, 0, 2, 1, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 2], [2, 1, 1, 4, 0, 1, 0, 0, 0, 1, 2, 1, 0, 1, 0, 0, 3, 0, 0, 2], [1, 0, 0, 1, 1, 2, 1, 0, 1, 2, 0, 0, 1, 0, 0, 0, 3, 0, 2, 2], [2, 1, 1, 2, 0, 0, 4, 1, 2, 0, 0, 3, 0, 4, 4, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "path": [[1, 7], [2, 7], [3, 8], [4, 9], [3, 10], [2, 11], [1, 11], [0, 12], [1, 13], [2, 13], [3, 12], [4, 12], [5, 11], [6, 10], [7, 9], [8, 8], [9, 9], [10, 10], [11, 11], [12, 12], [13, 11], [14, 10], [13, 9], [12, 8], [11, 7], [10, 6], [9, 5], [8, 4], [7, 3], [6, 4], [5, 5], [4, 6], [3, 7], [2, 8], [3, 9], [4, 10], [5, 11], [6, 12], [7, 13], [8, 12], [9, 11], [10, 10], [11, 9], [12, 8], [13, 7], [14, 6], [15, 6], [16, 7], [17, 8], [18, 7]]}}, "_generation_metadata": {"difficulty": 27, "difficulty_factors": {"difficulty": 27, "normalized_difficulty": 0.3, "base_count": 11.0, "base_delay": 93.0, "complexity": 0.51, "buildable_space": 0.665, "strategic_terrain_density": 0.28, "health_scale": 0.125, "speed_scale": 0.045, "obstacle_density": 0.33499999999999996}, "generation_timestamp": null, "algorithm_version": "1.0", "generation_method": "modular_ai_full", "modular_components": {"map_structure": "ai", "framework": "ai_guided_procedural", "economic_system": "ai", "wave_progression": "ai", "terrain_strategy": "ai", "enemy_strategy": "ai", "theme_and_naming": "ai"}, "creation_type": "🧩 FULL AI MAP GENERATION", "reliability": "high"}, "_ai_map_strategy": {"path_strategy": "maze", "path_length_target": 50, "path_complexity": 0.8, "terrain_strategy": "open", "buildable_space_target": 0.85, "strategic_focus": "learning", "layout_reasoning": "Given the player's beginner skill level and low scores, a maze path with high complexity and long routing offers a forgiving environment to learn tower placement and enemy management. The open terrain maximizes buildable space to encourage exploration of tower diversity without strategic constraints, supporting the player's progression and learning focus."}, "_ai_metadata": {"economic_focus": "economy_boost", "economic_reasoning": "Player struggled significantly, suggesting the need for a major economic boost to improve progression. Increased starting money and wave bonuses to provide immediate resources. Enhanced enemy reward scaling for better long-term economic growth. Adjusted enemy spawn rates to correct reward imbalances and added strategic focus on boosting economy while keeping challenge intact."}, "_original_economic_values": {"starting_money": 200, "normal_wave_bonus": 65, "boss_wave_bonus": 250, "reward_per_wave": 0.144, "max_reward_multiplier": 22.0}, "_ai_wave_strategy": {"total_waves_modifier": 0.6, "enemy_health_scaling_modifier": 0.8, "enemy_speed_scaling_modifier": 0.9, "enemy_reward_scaling_modifier": 0.9, "enemy_size_scaling_modifier": 0.9, "enemy_damage_scaling_modifier": 0.9, "max_health_multiplier_modifier": 0.8, "max_speed_multiplier_modifier": 0.8, "max_reward_multiplier_modifier": 0.8, "max_size_multiplier_modifier": 0.9, "max_damage_multiplier_modifier": 0.8, "enemy_max_health_modifier": 0.8, "enemy_max_speed_modifier": 0.9, "min_spawn_delay_modifier": 1.2, "base_spawn_delay_modifier": 1.2, "spawn_delay_reduction_modifier": 0.8, "boss_wave_frequency_modifier": 0.8, "difficulty_modifier": 0.9, "wave_progression_reasoning": "The player lost with a 6.0% score on difficulty 50, indicating significant difficulty. To help them succeed, the wave count is reduced by a major reduction factor (0.6x) to lessen the overall challenge. Enemy health, speed, reward, size, and damage scaling are reduced to make enemies more manageable. Max multipliers are scaled down to prevent excessive growth in enemy stats across waves. Spawn delays are increased to provide the player with more time to react. Boss wave frequency is also reduced to decrease the challenge of boss encounters. Overall, these adjustments aim to create a more forgiving progression curve, allowing the player to improve their strategy and succeed in the game."}, "_ai_terrain_strategy": {"path_type": "normal_path", "terrain_reasoning": "The player struggled significantly on a standard configuration with no tower diversity, indicating that a simpler path layout can help focus on learning tower placement and management. Reducing complexity will allow the player to better understand basic strategies and improve their score."}, "_ai_enemy_strategy": {"primary_counter_enemies": ["BasicEnemy", "TankEnemy", "FlyingEnemy", "ArmoredEnemy", "FastEnemy", "EnergyShieldEnemy", "ToxicEnemy", "FireElementalEnemy", "PhaseShiftEnemy", "VoidEnemy", "TeleportingEnemy", "SplittingEnemy", "SpectralEnemy", "CrystallineEnemy", "AdaptiveEnemy"], "strategy_focus": "Beginner level focus with supportive challenges, emphasizing strategic puzzles using selective counter-enemies without overwhelming variety.", "extreme_spawn_preference": "late_game_focus", "spawn_distribution_style": "polarized_extreme"}, "enemy_buff_config": {"description": "AI-generated buff configuration based on general performance", "enabled": true, "scenario_type": "adaptive", "buff_intensity": "low", "custom_spawn_rates": {"wave_ranges": {"1-10": {"base_chance": 0.025, "max_buffs": 1, "allowed_buffs": ["speed_boost", "armor"]}, "11-20": {"base_chance": 0.075, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration"]}, "21-30": {"base_chance": 0.125, "max_buffs": 3, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]}, "31-40": {"base_chance": 0.175, "max_buffs": 4, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance"]}, "41-50": {"base_chance": 0.225, "max_buffs": 5, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}, "51+": {"base_chance": 0.3, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}}, "boss_multipliers": {"mini_boss": 1.0, "boss": 1.5, "super_boss": 2.0}}, "featured_combinations": ["stealth_assassin", "flying_fortress"], "buff_metrics_tracking": {"track_buff_effectiveness": true, "track_tower_counters": true, "track_player_adaptation": true}, "generation_metadata": {"generated_from": "general_performance", "intensity_reasoning": "Based on 6.0% score and loss", "fallback_config": true}}, "level_name": "Strategic Step", "level_description": "Navigate through simplified strategic puzzles designed for beginners, focusing on selective counter-enemies to build foundational skills without overwhelming variety.", "_adaptive_metadata": {"ai_adjustments": {"difficulty_adjustment": {"new_difficulty": 27, "change": -3, "reasoning": "Initial difficulty based on performance score 6.0%"}, "map_structure_adjustments": {"path_strategy": "maze", "path_length_target": 50, "path_complexity": 0.8, "terrain_strategy": "open", "buildable_space_target": 0.85, "strategic_focus": "learning", "layout_reasoning": "Given the player's beginner skill level and low scores, a maze path with high complexity and long routing offers a forgiving environment to learn tower placement and enemy management. The open terrain maximizes buildable space to encourage exploration of tower diversity without strategic constraints, supporting the player's progression and learning focus."}, "economic_adjustments": {"starting_money_modifier": 1.2, "normal_wave_bonus_modifier": 1.3, "boss_wave_bonus_modifier": 1.25, "enemy_reward_scaling_modifier": 1.2, "max_reward_multiplier_modifier": 1.1, "tower_cost_progression": {"early_increase_per_wave_modifier": 1.0, "mid_increase_per_wave_modifier": 1.5, "late_increase_per_wave_modifier": 2.0, "max_cost_multiplier_modifier": 1.5}, "enemy_spawn_rate_adjustments": {"BasicEnemy": 2.0, "TankEnemy": 0.5, "FlyingEnemy": 1.8, "InvisibleEnemy": 0.6, "SpectralEnemy": 2.5, "VoidEnemy": 0.3, "AdaptiveEnemy": 3.0}, "strategic_focus": "economy_boost", "reasoning": "Player struggled significantly, suggesting the need for a major economic boost to improve progression. Increased starting money and wave bonuses to provide immediate resources. Enhanced enemy reward scaling for better long-term economic growth. Adjusted enemy spawn rates to correct reward imbalances and added strategic focus on boosting economy while keeping challenge intact."}, "wave_adjustments": {"total_waves_modifier": 0.6, "enemy_health_scaling_modifier": 0.8, "enemy_speed_scaling_modifier": 0.9, "enemy_reward_scaling_modifier": 0.9, "enemy_size_scaling_modifier": 0.9, "enemy_damage_scaling_modifier": 0.9, "max_health_multiplier_modifier": 0.8, "max_speed_multiplier_modifier": 0.8, "max_reward_multiplier_modifier": 0.8, "max_size_multiplier_modifier": 0.9, "max_damage_multiplier_modifier": 0.8, "enemy_max_health_modifier": 0.8, "enemy_max_speed_modifier": 0.9, "min_spawn_delay_modifier": 1.2, "base_spawn_delay_modifier": 1.2, "spawn_delay_reduction_modifier": 0.8, "boss_wave_frequency_modifier": 0.8, "difficulty_modifier": 0.9, "wave_progression_reasoning": "The player lost with a 6.0% score on difficulty 50, indicating significant difficulty. To help them succeed, the wave count is reduced by a major reduction factor (0.6x) to lessen the overall challenge. Enemy health, speed, reward, size, and damage scaling are reduced to make enemies more manageable. Max multipliers are scaled down to prevent excessive growth in enemy stats across waves. Spawn delays are increased to provide the player with more time to react. Boss wave frequency is also reduced to decrease the challenge of boss encounters. Overall, these adjustments aim to create a more forgiving progression curve, allowing the player to improve their strategy and succeed in the game."}, "terrain_adjustments": {"path_type": "normal_path", "terrain_reasoning": "The player struggled significantly on a standard configuration with no tower diversity, indicating that a simpler path layout can help focus on learning tower placement and management. Reducing complexity will allow the player to better understand basic strategies and improve their score."}, "enemy_adjustments": {"primary_counter_enemies": ["BasicEnemy", "TankEnemy", "FlyingEnemy", "ArmoredEnemy", "FastEnemy", "EnergyShieldEnemy", "ToxicEnemy", "FireElementalEnemy", "PhaseShiftEnemy", "VoidEnemy", "TeleportingEnemy", "SplittingEnemy", "SpectralEnemy", "CrystallineEnemy", "AdaptiveEnemy"], "strategy_focus": "Beginner level focus with supportive challenges, emphasizing strategic puzzles using selective counter-enemies without overwhelming variety.", "extreme_spawn_preference": "late_game_focus", "spawn_distribution_style": "polarized_extreme"}, "reasoning": "Complete AI-driven generation: Map structure, economic system, wave progression, terrain, enemies, and theming all designed by AI for performance score 6.0% (Final difficulty: 27)", "buff_system_adjustments": {"enabled": true, "intensity": "low", "reasoning": "AI-generated buff configuration"}}, "generation_timestamp": "2025-07-11T11:32:20.759130", "generation_method": "modular_ai_multi_game", "creation_type": "🧩 FULL AI MAP GENERATION", "multi_game_context": {"games_analyzed": 1, "avg_score": 6.0, "win_rate": 0.0, "trend": "stable", "difficulty_progression": [50], "strategic_patterns": {"tower_effectiveness": {}, "most_preferred_strategy": null, "strategy_consistency": 0.0, "economic_patterns": {"avg_economic_efficiency": 0.0, "avg_resource_management": 0.0, "economic_trend": "stable"}, "tower_diversity_trend": [0], "games_analyzed": 1}, "performance_trends": {"score_trend": "stable", "win_rate": 0.0, "avg_score": 6.0, "avg_wave_progression": 100.0, "problem_areas": ["low_win_rate", "low_scores"], "strengths": ["strong_progression"], "consistency": 1.0, "recent_performance": {"last_3_scores": [6.0], "last_3_wins": [false], "improvement_rate": 0}}, "performance_summaries": [{"score": 6.0, "win_flag": false, "lives_remaining": 0, "starting_lives": 20, "towers_built": {}, "tower_diversity": 0, "wave_reached": 3, "final_wave": 3, "economic_efficiency": 0.0, "resource_management_score": 0.0, "most_built_tower_type": null, "config_difficulty_score": null, "previous_config_details": {}}]}}, "_analytical_balancing": {"applied": true, "problematic_waves": 0, "waves_analyzed": 20, "tower_catalog_size": 14, "path_length": 67.22539674441613, "enemy_speed": 1.0}}