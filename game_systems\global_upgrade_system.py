import json
import os
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class UpgradeType(Enum):
    DAMAGE = "damage"
    RANGE = "range"
    FIRE_RATE = "fire_rate"
    PROJECTILE_SPEED = "projectile_speed"

# Upgrade limits to prevent towers from becoming too powerful
UPGRADE_LIMITS = {
    UpgradeType.DAMAGE: 10,        # Max +10 damage
    UpgradeType.RANGE: 8,          # Max +40 range
    UpgradeType.FIRE_RATE: 6,      # Max +12 fire rate (lower is faster)
    UpgradeType.PROJECTILE_SPEED: 5  # Max +0.5 projectile speed
}

@dataclass
class TowerUpgrade:
    """Represents a single tower upgrade"""
    tower_type: str
    upgrade_type: UpgradeType
    level: int
    cost: int
    effect_value: float
    description: str

@dataclass
class GlobalUpgradeData:
    """Global upgrade data for a specific tower type"""
    tower_type: str
    damage_level: int = 0
    range_level: int = 0
    fire_rate_level: int = 0
    projectile_speed_level: int = 0
    
    def get_total_level(self) -> int:
        """Get total upgrade level for this tower"""
        return self.damage_level + self.range_level + self.fire_rate_level + self.projectile_speed_level

@dataclass
class TowerUpgradeData:
    """Upgrade data for a specific tower instance"""
    tower_id: str
    tower_type: str
    damage_level: int = 0
    range_level: int = 0
    fire_rate_level: int = 0
    projectile_speed_level: int = 0
    
    def get_total_level(self) -> int:
        """Get total upgrade level for this tower"""
        return self.damage_level + self.range_level + self.fire_rate_level + self.projectile_speed_level

class GlobalUpgradeSystem:
    """Manages global tower upgrades that persist between games (per tower type)"""
    
    def __init__(self, save_file_path: str = "global_upgrades/global_upgrades.json"):
        self.save_file_path = save_file_path
        self.points = 0
        self.total_points_earned = 0
        self.games_won = 0
        self.tower_types = [
            'basic', 'sniper', 'freezer', 'detector', 'antiair', 'poison', 
            'laser', 'cannon', 'lightning', 'flame', 'ice', 'explosive', 
            'missile', 'splash', 'destroyer'
        ]
        self.upgrades: Dict[str, GlobalUpgradeData] = {tt: GlobalUpgradeData(tt) for tt in self.tower_types}
        self.load_data()
    
    def calculate_victory_points(self, difficulty: int) -> int:
        """Calculate points earned from victory based on difficulty"""
        # Base formula: 15 points for difficulty 100, 1 point for difficulty 1
        # Linear interpolation between these points
        if difficulty <= 1:
            return 1
        elif difficulty >= 100:
            return 15
        else:
            # Linear interpolation: 1 + (difficulty - 1) * (14 / 99)
            return 1 + int((difficulty - 1) * (14 / 99))
    
    def award_victory_points(self, difficulty: int) -> int:
        """Award points for winning a game and save data"""
        points_earned = self.calculate_victory_points(difficulty)
        self.points += points_earned
        self.total_points_earned += points_earned
        self.games_won += 1
        self.save_data()
        return points_earned
    
    def get_upgrade_cost(self, tower_type: str, upgrade_type: UpgradeType) -> int:
        """Calculate cost for next upgrade level for a specific tower"""
        current_level = self.get_upgrade_level(tower_type, upgrade_type)
        base_cost = 5  # Base cost for first upgrade
        
        # Exponential cost scaling: base_cost * (1.5 ^ current_level)
        return int(base_cost * (1.5 ** current_level))
    
    def get_upgrade_level(self, tower_type: str, upgrade_type: UpgradeType) -> int:
        """Get current upgrade level for a specific tower and upgrade type"""
        if tower_type not in self.upgrades:
            return 0
        
        upgrade_data = self.upgrades[tower_type]
        if upgrade_type == UpgradeType.DAMAGE:
            return upgrade_data.damage_level
        elif upgrade_type == UpgradeType.RANGE:
            return upgrade_data.range_level
        elif upgrade_type == UpgradeType.FIRE_RATE:
            return upgrade_data.fire_rate_level
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            return upgrade_data.projectile_speed_level
        return 0
    
    def get_upgrade_effect(self, tower_type: str, upgrade_type: UpgradeType) -> float:
        """Get the current effect value for an upgrade"""
        level = self.get_upgrade_level(tower_type, upgrade_type)
        
        # Effect scaling per level - much smaller effects
        if upgrade_type == UpgradeType.DAMAGE:
            return level * 1  # +1 damage per level
        elif upgrade_type == UpgradeType.RANGE:
            return level * 5  # +5 range per level
        elif upgrade_type == UpgradeType.FIRE_RATE:
            return level * 2  # +2 fire rate per level (lower is faster)
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            return level * 0.1  # +0.1 projectile speed per level
        return 0.0
    
    def can_afford_upgrade(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Check if player can afford an upgrade for a specific tower"""
        cost = self.get_upgrade_cost(tower_type, upgrade_type)
        return self.points >= cost
    
    def is_upgrade_at_limit(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Check if an upgrade has reached its maximum level"""
        current_level = self.get_upgrade_level(tower_type, upgrade_type)
        max_level = UPGRADE_LIMITS.get(upgrade_type, 0)
        return current_level >= max_level
    
    def can_purchase_upgrade(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Check if player can purchase an upgrade (affordable and not at limit)"""
        return self.can_afford_upgrade(tower_type, upgrade_type) and not self.is_upgrade_at_limit(tower_type, upgrade_type)
    
    def purchase_upgrade(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Purchase an upgrade for a specific tower if affordable and not at limit"""
        if not self.can_purchase_upgrade(tower_type, upgrade_type):
            return False
        
        cost = self.get_upgrade_cost(tower_type, upgrade_type)
        self.points -= cost
        
        # Apply the upgrade
        upgrade_data = self.upgrades[tower_type]
        if upgrade_type == UpgradeType.DAMAGE:
            upgrade_data.damage_level += 1
        elif upgrade_type == UpgradeType.RANGE:
            upgrade_data.range_level += 1
        elif upgrade_type == UpgradeType.FIRE_RATE:
            upgrade_data.fire_rate_level += 1
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            upgrade_data.projectile_speed_level += 1
        
        self.save_data()
        return True
    
    def get_tower_upgrade_info(self, tower_type: str) -> Dict[str, Dict]:
        """Get comprehensive upgrade information for a specific tower"""
        upgrade_data = self.upgrades[tower_type]
        info = {}
        
        for upgrade_type in UpgradeType:
            level = self.get_upgrade_level(tower_type, upgrade_type)
            cost = self.get_upgrade_cost(tower_type, upgrade_type)
            effect = self.get_upgrade_effect(tower_type, upgrade_type)
            can_afford = self.can_afford_upgrade(tower_type, upgrade_type)
            is_at_limit = self.is_upgrade_at_limit(tower_type, upgrade_type)
            can_purchase = self.can_purchase_upgrade(tower_type, upgrade_type)
            max_level = UPGRADE_LIMITS.get(upgrade_type, 0)
            
            info[upgrade_type.value] = {
                'level': level,
                'max_level': max_level,
                'cost': cost,
                'effect': effect,
                'can_afford': can_afford,
                'is_at_limit': is_at_limit,
                'can_purchase': can_purchase,
                'description': self.get_upgrade_description(tower_type, upgrade_type, level, effect)
            }
        
        return info
    
    def get_upgrade_description(self, tower_type: str, upgrade_type: UpgradeType, level: int, effect: float) -> str:
        """Generate description for an upgrade"""
        max_level = UPGRADE_LIMITS.get(upgrade_type, 0)
        limit_text = f" (MAX)" if level >= max_level else f" (Level {level}/{max_level})"
        
        if upgrade_type == UpgradeType.DAMAGE:
            return f"Damage +{int(effect)}{limit_text}"
        elif upgrade_type == UpgradeType.RANGE:
            return f"Range +{int(effect)}{limit_text}"
        elif upgrade_type == UpgradeType.FIRE_RATE:
            return f"Fire Rate +{int(effect)}{limit_text}"
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            return f"Projectile Speed +{effect:.1f}{limit_text}"
        return f"Unknown Upgrade{limit_text}"
    
    def apply_upgrades_to_tower(self, tower) -> None:
        """Apply upgrades to a specific tower instance"""
        if not hasattr(tower, 'tower_type') or tower.tower_type not in self.upgrades:
            return
        
        upgrade_data = self.upgrades[tower.tower_type]
        
        # Apply damage upgrade
        if upgrade_data.damage_level > 0:
            damage_bonus = self.get_upgrade_effect(tower.tower_type, UpgradeType.DAMAGE)
            tower.damage += int(damage_bonus)
        
        # Apply range upgrade
        if upgrade_data.range_level > 0:
            range_bonus = self.get_upgrade_effect(tower.tower_type, UpgradeType.RANGE)
            tower.range += int(range_bonus)
        
        # Apply fire rate upgrade (lower is faster)
        if upgrade_data.fire_rate_level > 0:
            fire_rate_bonus = self.get_upgrade_effect(tower.tower_type, UpgradeType.FIRE_RATE)
            tower.fire_rate = max(10, tower.fire_rate - int(fire_rate_bonus))  # Don't go below 10
        
        # Apply projectile speed upgrade
        if upgrade_data.projectile_speed_level > 0:
            speed_bonus = self.get_upgrade_effect(tower.tower_type, UpgradeType.PROJECTILE_SPEED)
            tower.projectile_speed += speed_bonus
    
    def get_statistics(self) -> Dict:
        """Get player statistics"""
        total_upgrades = sum(data.get_total_level() for data in self.upgrades.values())
        
        return {
            'points': self.points,
            'total_points_earned': self.total_points_earned,
            'games_won': self.games_won,
            'total_upgrades_purchased': total_upgrades,
            'upgrades_by_tower': {tower_type: data.get_total_level() for tower_type, data in self.upgrades.items()}
        }
    
    def save_data(self) -> None:
        """Save upgrade data to file"""
        data = {
            'points': self.points,
            'total_points_earned': self.total_points_earned,
            'games_won': self.games_won,
            'upgrades': {tower_type: asdict(upgrade_data) for tower_type, upgrade_data in self.upgrades.items()}
        }
        
        try:
            # Ensure the directory exists
            import os
            os.makedirs(os.path.dirname(self.save_file_path), exist_ok=True)
            
            with open(self.save_file_path, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Failed to save global upgrade data: {e}")
    
    def load_data(self) -> None:
        """Load upgrade data from file"""
        if not os.path.exists(self.save_file_path):
            print(f"DEBUG: No save file found at {self.save_file_path}")
            return
        
        try:
            with open(self.save_file_path, 'r') as f:
                data = json.load(f)
            
            print(f"DEBUG: Loading data from {self.save_file_path}")
            print(f"DEBUG: Loaded points: {data.get('points', 0)}")
            print(f"DEBUG: Loaded upgrades keys: {list(data.get('upgrades', {}).keys())}")
            if 'basic' in data.get('upgrades', {}):
                print(f"DEBUG: Basic tower data from JSON: {data['upgrades']['basic']}")
            
            self.points = data.get('points', 0)
            self.total_points_earned = data.get('total_points_earned', 0)
            self.games_won = data.get('games_won', 0)
            
            # Migrate old per-instance data if present
            upgrades_data = data.get('upgrades', {})
            if upgrades_data and isinstance(list(upgrades_data.values())[0], dict) and 'tower_type' in list(upgrades_data.values())[0]:
                # Per-type data
                print(f"DEBUG: Loading per-type data")
                for tower_type, upgrade_data in upgrades_data.items():
                    if tower_type in self.tower_types:
                        self.upgrades[tower_type] = GlobalUpgradeData(
                            tower_type=tower_type,
                            damage_level=upgrade_data.get('damage_level', 0),
                            range_level=upgrade_data.get('range_level', 0),
                            fire_rate_level=upgrade_data.get('fire_rate_level', 0),
                            projectile_speed_level=upgrade_data.get('projectile_speed_level', 0)
                        )
                        print(f"DEBUG: Loaded {tower_type}: damage={upgrade_data.get('damage_level', 0)}, range={upgrade_data.get('range_level', 0)}")
            else:
                # Per-instance data: migrate to per-type by taking max for each type
                print(f"DEBUG: Loading per-instance data")
                per_type = {tt: GlobalUpgradeData(tt) for tt in self.tower_types}
                for instance_data in upgrades_data.values():
                    tt = instance_data.get('tower_type')
                    if tt in per_type:
                        per_type[tt].damage_level = max(per_type[tt].damage_level, instance_data.get('damage_level', 0))
                        per_type[tt].range_level = max(per_type[tt].range_level, instance_data.get('range_level', 0))
                        per_type[tt].fire_rate_level = max(per_type[tt].fire_rate_level, instance_data.get('fire_rate_level', 0))
                        per_type[tt].projectile_speed_level = max(per_type[tt].projectile_speed_level, instance_data.get('projectile_speed_level', 0))
                self.upgrades = per_type
        except Exception as e:
            print(f"Failed to load global upgrade data: {e}")
    
    def reset_data(self) -> None:
        """Reset all upgrade data (for testing or fresh start)"""
        self.points = 0
        self.total_points_earned = 0
        self.games_won = 0
        self.upgrades = {tt: GlobalUpgradeData(tt) for tt in self.tower_types}
        self.save_data()
    
    def get_all_upgraded_towers(self) -> List[Dict]:
        """Get list of all upgraded towers for main menu display"""
        upgraded_towers = []
        for tower_type, data in self.upgrades.items():
            upgraded_towers.append({
                'tower_type': tower_type,
                'tower_id': tower_type,
                'total_level': data.get_total_level(),
                'damage_level': data.damage_level,
                'range_level': data.range_level,
                'fire_rate_level': data.fire_rate_level,
                'projectile_speed_level': data.projectile_speed_level
            })
        
        # Sort by total upgrade level (highest first)
        upgraded_towers.sort(key=lambda x: x['total_level'], reverse=True)
        return upgraded_towers
    
    def get_tower_type_summary(self) -> Dict[str, Dict]:
        """Get summary of upgrades by tower type for main menu"""
        summary = {}
        for tower_type in self.tower_types:
            data = self.upgrades[tower_type]
            summary[tower_type] = {
                'total_upgrades': data.get_total_level(),
                'towers_count': 1 if data.get_total_level() > 0 else 0,
                'avg_level': data.get_total_level()
            }
        
        return summary
    
    def create_tower_from_menu(self, tower_type: str = 'basic') -> str:
        """Create a new tower entry from the main menu for upgrading"""
        import time
        
        # Generate unique tower ID
        tower_id = f"{tower_type}_{int(time.time())}"
        
        # Create tower upgrade data
        self.upgrades[tower_type] = GlobalUpgradeData(
            tower_type=tower_type
        )
        
        # Save the data
        self.save_data()
        
        return tower_id 