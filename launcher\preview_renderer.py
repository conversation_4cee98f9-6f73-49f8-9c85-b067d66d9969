"""
Preview Renderer for Tower Defense Game Launcher
Handles drawing of level preview screens
"""

import pygame
from typing import Dict, List, Any, Optional


class PreviewRenderer:
    """Handles drawing of level preview screens"""
    
    def __init__(self, colors, fonts, screen_width: int, screen_height: int):
        """Initialize with color scheme, fonts, and screen dimensions"""
        self.colors = colors
        self.fonts = fonts
        self.screen_width = screen_width
        self.screen_height = screen_height
    
    def draw_level_preview(self, screen, navigation_manager):
        """Draw the level preview screen with description and rewards"""
        if not navigation_manager.preview_config:
            return
        
        # Background
        screen.fill(self.colors['BACKGROUND'])
        
        # Get level metadata
        metadata = navigation_manager.preview_config.get('level_metadata', {})
        
        # Check if this is a variant
        is_variant = '_variant_metadata' in navigation_manager.preview_config
        variant_metadata = navigation_manager.preview_config.get('_variant_metadata', {}) if is_variant else {}
        
        # Header section
        header_height = 100
        pygame.draw.rect(screen, self.colors['CARD_BG'], (0, 0, self.screen_width, header_height))
        pygame.draw.line(screen, self.colors['BORDER_LIGHT'], (0, header_height), (self.screen_width, header_height), 1)
        
        # Back button
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 30
        pygame.draw.rect(screen, self.colors['BORDER_DARK'], (back_btn_x, back_btn_y, back_btn_width, back_btn_height), border_radius=8)
        pygame.draw.rect(screen, self.colors['CARD_BG'], (back_btn_x + 1, back_btn_y + 1, back_btn_width - 2, back_btn_height - 2), border_radius=7)
        back_text = self.fonts['info'].render("← Back", True, self.colors['TEXT_PRIMARY'])
        back_rect = back_text.get_rect(center=(back_btn_x + back_btn_width // 2, back_btn_y + back_btn_height // 2))
        screen.blit(back_text, back_rect)
        
        # Level title
        if is_variant:
            level_name = variant_metadata.get('variant_name', navigation_manager.preview_config.get('name', 'Unknown Variant'))
        else:
            level_name = metadata.get('name', navigation_manager.preview_config.get('name', 'Unknown Level'))
        title_text = self.fonts['title'].render(level_name, True, self.colors['TEXT_PRIMARY'])
        title_rect = title_text.get_rect(center=(self.screen_width // 2, 35))
        screen.blit(title_text, title_rect)
        
        # Difficulty rating and variant indicator
        if is_variant:
            base_level = variant_metadata.get('base_level', 'Unknown')
            difficulty_mult = variant_metadata.get('difficulty_multiplier', 1.0)
            reward_mult = variant_metadata.get('reward_multiplier', 1.0)
            difficulty_text = self.fonts['info'].render(f"Variant of {base_level} • {difficulty_mult:.1f}x difficulty • {reward_mult:.1f}x reward", True, (138, 43, 226))
        else:
            difficulty_rating = metadata.get('difficulty_rating', 'Unknown')
            difficulty_color = self.colors['ACCENT_GREEN'] if difficulty_rating == 'Easy' else self.colors['ACCENT_ORANGE'] if difficulty_rating == 'Normal' else (220, 53, 69)
            difficulty_text = self.fonts['info'].render(f"Difficulty: {difficulty_rating}", True, difficulty_color)
        
        difficulty_rect = difficulty_text.get_rect(center=(self.screen_width // 2, 65))
        screen.blit(difficulty_text, difficulty_rect)
        
        # Main content area
        content_margin = 80
        content_width = self.screen_width - 2 * content_margin
        content_x = content_margin
        content_y = header_height + 40
        
        # Description section
        if is_variant:
            desc_title = self.fonts['subtitle'].render("Variant Modifiers", True, self.colors['TEXT_PRIMARY'])
            screen.blit(desc_title, (content_x, content_y))
            content_y += 35
            
            # Show modifiers applied
            modifiers = variant_metadata.get('modifiers', [])
            if modifiers:
                for modifier in modifiers:
                    mod_name = modifier.get('name', 'Unknown Modifier')
                    mod_desc = modifier.get('description', 'No description')
                    
                    # Modifier name
                    mod_text = self.fonts['menu'].render(f"• {mod_name}", True, self.colors['TEXT_PRIMARY'])
                    screen.blit(mod_text, (content_x + 20, content_y))
                    content_y += 25
                    
                    # Modifier description
                    desc_lines = self._wrap_text(mod_desc, self.fonts['info'], content_width - 60)
                    for line in desc_lines:
                        desc_text = self.fonts['info'].render(line, True, self.colors['TEXT_SECONDARY'])
                        screen.blit(desc_text, (content_x + 40, content_y))
                        content_y += 20
                    content_y += 10
            else:
                no_mods_text = self.fonts['menu'].render("No modifiers applied", True, self.colors['TEXT_SECONDARY'])
                screen.blit(no_mods_text, (content_x + 20, content_y))
                content_y += 30
        else:
            desc_title = self.fonts['subtitle'].render("Description", True, self.colors['TEXT_PRIMARY'])
            screen.blit(desc_title, (content_x, content_y))
            content_y += 35
            
            # Description text (wrap to multiple lines)
            description = metadata.get('description', 'No description available.')
            desc_lines = self._wrap_text(description, self.fonts['menu'], content_width - 40)
            for line in desc_lines:
                desc_text = self.fonts['menu'].render(line, True, self.colors['TEXT_SECONDARY'])
                screen.blit(desc_text, (content_x + 20, content_y))
                content_y += 25
        
        content_y += 20
        
        # Level details section
        details_title = self.fonts['subtitle'].render("Level Details", True, self.colors['TEXT_PRIMARY'])
        screen.blit(details_title, (content_x, content_y))
        content_y += 35
        
        # Create two columns for details
        col1_x = content_x + 20
        col2_x = content_x + content_width // 2 + 20
        
        if is_variant:
            # Column 1: Base level info
            base_level_text = self.fonts['info'].render(f"Base Level: {variant_metadata.get('base_level', 'Unknown')}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(base_level_text, (col1_x, content_y))
            
            generation_time = variant_metadata.get('generation_timestamp', '')
            if generation_time:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(generation_time.replace('Z', '+00:00'))
                    time_str = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    time_str = 'Unknown'
            else:
                time_str = 'Unknown'
            time_text = self.fonts['info'].render(f"Created: {time_str}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(time_text, (col1_x, content_y + 25))
            
            # Column 2: Difficulty and reward multipliers
            base_diff = variant_metadata.get('base_difficulty', 50)
            final_diff = variant_metadata.get('final_difficulty', 50)
            diff_text = self.fonts['info'].render(f"Difficulty: {base_diff} → {final_diff}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(diff_text, (col2_x, content_y))
            
            estimated_time = variant_metadata.get('estimated_playtime', 'Unknown')
            duration_text = self.fonts['info'].render(f"Duration: {estimated_time}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(duration_text, (col2_x, content_y + 25))
        else:
            # Column 1: Duration, Recommended for (original behavior)
            duration_text = self.fonts['info'].render(f"Duration: {metadata.get('estimated_duration', 'Unknown')}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(duration_text, (col1_x, content_y))
            
            recommended_text = self.fonts['info'].render(f"Recommended for: {metadata.get('recommended_for', 'all players')}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(recommended_text, (col1_x, content_y + 25))
            
            # Column 2: Difficulty score, Victory points
            difficulty_score = navigation_manager.preview_config.get('difficulty', 50)
            score_text = self.fonts['info'].render(f"Difficulty Score: {difficulty_score}/100", True, self.colors['TEXT_SECONDARY'])
            screen.blit(score_text, (col2_x, content_y))
            
            victory_points = metadata.get('victory_rewards', {}).get('victory_points', 1)
            points_text = self.fonts['info'].render(f"Victory Points: {victory_points}", True, self.colors['ACCENT_GREEN'])
            screen.blit(points_text, (col2_x, content_y + 25))
        
        content_y += 70
        
        # Rewards section
        rewards_title = self.fonts['subtitle'].render("Victory Rewards", True, self.colors['TEXT_PRIMARY'])
        screen.blit(rewards_title, (content_x, content_y))
        content_y += 35
        
        if is_variant:
            # Calculate variant rewards
            base_victory_points = metadata.get('victory_rewards', {}).get('victory_points', 1)
            reward_mult = variant_metadata.get('reward_multiplier', 1.0)
            variant_points = int(base_victory_points * reward_mult)
            
            points_desc = f"Earn {variant_points} Victory Points ({base_victory_points} × {reward_mult:.1f}x modifier)"
            points_desc_text = self.fonts['menu'].render(points_desc, True, self.colors['ACCENT_GREEN'])
            screen.blit(points_desc_text, (content_x + 20, content_y))
            content_y += 30
            
            # Variant completion bonuses
            bonuses = [
                "Bonus Victory Points for difficulty",
                "Achievement progress",
                "Variant mastery experience"
            ]
            for bonus in bonuses[:3]:
                bonus_text = self.fonts['info'].render(f"• {bonus}", True, self.colors['TEXT_SECONDARY'])
                screen.blit(bonus_text, (content_x + 20, content_y))
                content_y += 22
        else:
            # Victory points description (original behavior)
            victory_rewards = metadata.get('victory_rewards', {})
            victory_points = victory_rewards.get('victory_points', 1)
            points_desc = victory_rewards.get('victory_points_description', f'Earn {victory_points} Victory Points')
            points_desc_text = self.fonts['menu'].render(points_desc, True, self.colors['ACCENT_GREEN'])
            screen.blit(points_desc_text, (content_x + 20, content_y))
            content_y += 30
            
            # Completion bonuses
            bonuses = victory_rewards.get('completion_bonuses', [])
            if bonuses:
                for bonus in bonuses[:3]:  # Show up to 3 bonuses
                    bonus_text = self.fonts['info'].render(f"• {bonus}", True, self.colors['TEXT_SECONDARY'])
                    screen.blit(bonus_text, (content_x + 20, content_y))
                    content_y += 22
        
        content_y += 20
        
        # Special features or tips section
        if is_variant:
            # Show variant-specific information
            features_title = self.fonts['subtitle'].render("Variant Info", True, self.colors['TEXT_PRIMARY'])
            screen.blit(features_title, (content_x, content_y))
            content_y += 35
            
            modifiers = variant_metadata.get('modifiers', [])
            info_items = [
                f"Modifiers applied: {len(modifiers)}",
                f"Difficulty multiplier: {variant_metadata.get('difficulty_multiplier', 1.0):.1f}x",
                f"Reward multiplier: {variant_metadata.get('reward_multiplier', 1.0):.1f}x"
            ]
            
            for item in info_items:
                item_text = self.fonts['info'].render(f"• {item}", True, self.colors['TEXT_SECONDARY'])
                screen.blit(item_text, (content_x + 20, content_y))
                content_y += 22
        else:
            # Special features section (original behavior)
            features = metadata.get('special_features', [])
            if features:
                features_title = self.fonts['subtitle'].render("Special Features", True, self.colors['TEXT_PRIMARY'])
                screen.blit(features_title, (content_x, content_y))
                content_y += 35
                
                for feature in features[:4]:  # Show up to 4 features
                    feature_text = self.fonts['info'].render(f"• {feature}", True, self.colors['TEXT_SECONDARY'])
                    screen.blit(feature_text, (content_x + 20, content_y))
                    content_y += 22
        
        # Play button (large, centered at bottom)
        play_btn_width = 200
        play_btn_height = 50
        play_btn_x = self.screen_width // 2 - play_btn_width // 2
        play_btn_y = self.screen_height - 120
        
        pygame.draw.rect(screen, self.colors['ACCENT_GREEN'], (play_btn_x, play_btn_y, play_btn_width, play_btn_height), border_radius=25)
        
        # Play button text
        play_text = self.fonts['subtitle'].render("START GAME", True, self.colors['CARD_BG'])
        play_rect = play_text.get_rect(center=(play_btn_x + play_btn_width // 2, play_btn_y + play_btn_height // 2))
        screen.blit(play_text, play_rect)
        
        # Tips section (if space allows and not a variant)
        if not is_variant:
            tips = metadata.get('tips', [])
            if tips and content_y < self.screen_height - 200:
                tips_title = self.fonts['subtitle'].render("Tips", True, self.colors['TEXT_PRIMARY'])
                screen.blit(tips_title, (content_x, content_y))
                content_y += 35
                
                for tip in tips[:2]:  # Show up to 2 tips
                    if content_y > self.screen_height - 180:
                        break
                    tip_text = self.fonts['info'].render(f"• {tip}", True, self.colors['TEXT_SECONDARY'])
                    screen.blit(tip_text, (content_x + 20, content_y))
                    content_y += 22
        
        pygame.display.flip()
    
    def _wrap_text(self, text: str, font, max_width: int) -> list:
        """Wrap text to fit within a given width"""
        words = text.split(' ')
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    lines.append(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
