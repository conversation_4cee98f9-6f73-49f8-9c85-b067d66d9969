{"calculated_difficulty": {"score": 70, "description": "Hard - Challenging configuration requiring strategy", "formula_components": {"starting_resources": 25.0, "enemy_scaling": 22.5, "spawn_progression": 10.3, "special_rounds": 8.6, "tower_economics": 4.0}, "analysis": {"starting_resources": "Very Low (20 money, 20 lives) - Maximum difficulty points", "enemy_scaling": "High (30% health per wave, up to 60x multiplier)", "spawn_progression": "Moderate (average 10.9 enemies added per round)", "special_rounds": "Strong (average 4.4x enemy multiplier)", "tower_costs": "Moderate escalation (up to 3x cost multiplier)", "boss_density": "High (12 boss waves out of 80 total)", "mathematical_formula": "Sum of 5 weighted components (0-100 scale)"}}, "level_metadata": {"name": "Infernal Gauntlet", "description": "A brutal 80-wave challenge that pushes even experienced defenders to their limits. Face relentless swarms of enemies with minimal starting resources, where every decision counts and strategic tower placement is crucial for survival. Only the most skilled commanders will emerge victorious from this hellish battlefield.", "difficulty_rating": "Hard", "estimated_duration": "45-60 minutes", "recommended_for": "experienced players", "special_features": ["80 challenging waves", "Low starting resources (20 money, 20 lives)", "High enemy scaling and boss density", "Advanced enemy types with special abilities", "Dynamic enemy buff system"], "victory_rewards": {"victory_points": 11, "victory_points_description": "<PERSON>arn 11 Victory Points for completing this challenging level", "unlock_requirements": [], "completion_bonuses": ["Unlock advanced tower upgrades", "Access to elite enemy strategies", "Prestigious completion achievement"]}, "tips": ["Prioritize economy early with basic towers", "Save money for critical anti-air defenses", "Use detector towers to counter invisible enemies", "Plan for multiple boss encounters"]}, "game_config": {"difficulty": "hard", "starting_money": 10, "starting_lives": 20}, "wave_config": {"total_waves": 80, "spawn_config": {"base_enemy_count": 5, "base_spawn_delay": 120, "min_spawn_delay": 0.1, "boss_enemy_count": 1}, "round_progression": {"enemy_increase_per_round": {"wave_ranges": {"1-5": 1, "6-10": 2, "11-15": 3, "16-20": 4, "21-25": 5, "26-30": 6, "31-35": 7, "36-40": 8, "41-45": 9, "46-50": 10, "51-55": 11, "56-60": 12, "61-65": 13, "66-70": 14, "71-75": 15, "76-80": 16}, "default": 1}, "spawn_delay_reduction_per_round": {"wave_ranges": {"1-10": 5, "11-20": 8, "21-30": 10, "31-40": 12, "41-50": 15, "51-60": 18, "61-70": 20, "71-80": 22}, "default": 5}, "special_rounds": {"10": {"enemy_multiplier": 1.5, "spawn_delay_multiplier": 0.8}, "20": {"enemy_multiplier": 2.0, "spawn_delay_multiplier": 0.7}, "30": {"enemy_multiplier": 2.5, "spawn_delay_multiplier": 0.6}, "40": {"enemy_multiplier": 3.0, "spawn_delay_multiplier": 0.5}, "50": {"enemy_multiplier": 4.0, "spawn_delay_multiplier": 0.4}, "60": {"enemy_multiplier": 5.0, "spawn_delay_multiplier": 0.3}, "70": {"enemy_multiplier": 6.0, "spawn_delay_multiplier": 0.25}, "80": {"enemy_multiplier": 8.0, "spawn_delay_multiplier": 0.2}}}, "wave_compositions": {"1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.2]], "6-10": [["BasicEnemy", 0.6], ["FastEnemy", 0.3], ["TankEnemy", 0.1]], "11-15": [["BasicEnemy", 0.4], ["FastEnemy", 0.3], ["TankEnemy", 0.2], ["FlyingEnemy", 0.1]], "16-20": [["BasicEnemy", 0.25], ["FastEnemy", 0.2], ["TankEnemy", 0.2], ["FlyingEnemy", 0.15], ["ShieldedEnemy", 0.15], ["ArmoredEnemy", 0.05]], "21-30": [["BasicEnemy", 0.15], ["FastEnemy", 0.15], ["TankEnemy", 0.15], ["FlyingEnemy", 0.1], ["ShieldedEnemy", 0.1], ["InvisibleEnemy", 0.1], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.09]], "31-40": [["FastEnemy", 0.12], ["TankEnemy", 0.12], ["FlyingEnemy", 0.1], ["ShieldedEnemy", 0.1], ["InvisibleEnemy", 0.08], ["RegeneratingEnemy", 0.08], ["TeleportingEnemy", 0.08], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.08], ["FireElementalEnemy", 0.04], ["ToxicEnemy", 0.04]], "41-50": [["TankEnemy", 0.12], ["FlyingEnemy", 0.12], ["ShieldedEnemy", 0.12], ["InvisibleEnemy", 0.12], ["RegeneratingEnemy", 0.12], ["TeleportingEnemy", 0.12], ["SplittingEnemy", 0.1], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.08], ["FireElementalEnemy", 0.07], ["ToxicEnemy", 0.07], ["BlastProofEnemy", 0.06]], "51-55": [["TankEnemy", 0.12], ["FlyingEnemy", 0.12], ["ShieldedEnemy", 0.12], ["InvisibleEnemy", 0.12], ["RegeneratingEnemy", 0.12], ["TeleportingEnemy", 0.12], ["SplittingEnemy", 0.09], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.07], ["GroundedEnemy", 0.07], ["FireElementalEnemy", 0.06], ["ToxicEnemy", 0.06], ["BlastProofEnemy", 0.05]], "56-60": [["TankEnemy", 0.11], ["FlyingEnemy", 0.11], ["ShieldedEnemy", 0.11], ["InvisibleEnemy", 0.11], ["RegeneratingEnemy", 0.11], ["TeleportingEnemy", 0.11], ["SplittingEnemy", 0.09], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.07], ["GroundedEnemy", 0.07], ["FireElementalEnemy", 0.06], ["ToxicEnemy", 0.06], ["BlastProofEnemy", 0.05]], "61-65": [["TankEnemy", 0.1], ["FlyingEnemy", 0.1], ["ShieldedEnemy", 0.1], ["InvisibleEnemy", 0.1], ["RegeneratingEnemy", 0.1], ["TeleportingEnemy", 0.1], ["SplittingEnemy", 0.09], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.08], ["FireElementalEnemy", 0.08], ["ToxicEnemy", 0.07], ["BlastProofEnemy", 0.07]], "66-70": [["TankEnemy", 0.08], ["FlyingEnemy", 0.08], ["ShieldedEnemy", 0.08], ["InvisibleEnemy", 0.08], ["RegeneratingEnemy", 0.08], ["TeleportingEnemy", 0.08], ["SplittingEnemy", 0.08], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.08], ["FireElementalEnemy", 0.08], ["ToxicEnemy", 0.08], ["BlastProofEnemy", 0.08]], "71-75": [["TankEnemy", 0.08], ["FlyingEnemy", 0.08], ["ShieldedEnemy", 0.08], ["InvisibleEnemy", 0.08], ["RegeneratingEnemy", 0.08], ["TeleportingEnemy", 0.08], ["SplittingEnemy", 0.08], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.08], ["FireElementalEnemy", 0.08], ["ToxicEnemy", 0.08], ["BlastProofEnemy", 0.08]], "76-80": [["TankEnemy", 0.08], ["FlyingEnemy", 0.08], ["ShieldedEnemy", 0.08], ["InvisibleEnemy", 0.08], ["RegeneratingEnemy", 0.08], ["TeleportingEnemy", 0.08], ["SplittingEnemy", 0.08], ["ArmoredEnemy", 0.08], ["EnergyShieldEnemy", 0.08], ["GroundedEnemy", 0.08], ["FireElementalEnemy", 0.08], ["ToxicEnemy", 0.08], ["BlastProofEnemy", 0.08]]}, "boss_waves": {"15": "SpeedBoss", "25": "SpeedBoss", "35": "MegaBoss", "40": "NecromancerBoss", "45": "MegaBoss", "50": "TimeLordBoss", "55": "SpeedBoss", "60": "ShadowKing", "65": "SpeedBoss", "70": "MegaBoss", "75": "CrystalOverlord", "80": "TimeLordBoss"}, "enemy_scaling": {"health_per_wave": 0.3, "speed_per_wave": 0.05, "reward_per_wave": 0.12, "size_per_wave": 0.02, "max_health_multiplier": 60.0, "max_speed_multiplier": 5.0, "max_reward_multiplier": 20.0, "max_size_multiplier": 2.0, "damage_scaling_per_wave": 0.1, "max_damage_multiplier": 4.0}, "money_config": {"normal_wave_bonus": 50, "boss_wave_bonus": 200}}, "map_config": {"default_map": {"width": 20, "height": 15, "terrain": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 2], [2, 0, 4, 4, 0, 0, 3, 1, 0, 0, 0, 0, 0, 1, 0, 3, 4, 4, 0, 2], [2, 0, 4, 4, 0, 0, 3, 1, 0, 0, 0, 0, 0, 1, 0, 3, 4, 4, 0, 2], [2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 2], [2, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2], [2, 0, 0, 5, 5, 0, 4, 4, 0, 0, 0, 0, 4, 4, 0, 5, 5, 0, 1, 2], [2, 0, 0, 5, 5, 0, 4, 4, 0, 3, 3, 0, 4, 4, 0, 5, 5, 0, 1, 2], [2, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 0, 0, 0, 0, 0, 0, 0, 1, 2], [2, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [2, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [2, 0, 4, 4, 0, 0, 3, 1, 0, 0, 0, 0, 0, 0, 0, 3, 4, 4, 0, 2], [2, 0, 4, 4, 0, 0, 3, 1, 0, 0, 0, 0, 0, 0, 0, 3, 4, 4, 0, 2], [2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "path": [[1, 5], [2, 5], [3, 5], [4, 5], [5, 5], [6, 5], [7, 5], [7, 4], [7, 3], [7, 2], [7, 1], [8, 1], [9, 1], [10, 1], [11, 1], [12, 1], [13, 1], [13, 2], [13, 3], [13, 4], [13, 5], [14, 5], [15, 5], [16, 5], [17, 5], [18, 5], [18, 6], [18, 7], [18, 8], [18, 9], [18, 10], [17, 10], [16, 10], [15, 10], [14, 10], [13, 10], [12, 10], [11, 10], [10, 10], [9, 10], [8, 10], [7, 10], [7, 11], [7, 12], [7, 13]]}}, "tower_config": {"base_costs": {"basic": 20, "sniper": 40, "cannon": 60, "freezer": 30, "poison": 35, "laser": 80, "lightning": 50, "missile": 100, "flame": 45, "ice": 55, "detector": 25, "antiair": 70, "explosive": 90, "splash": 120, "destroyer": 110}, "cost_progression": {"early_game_waves": 15, "mid_game_waves": 30, "early_increase_per_wave": 0.02, "mid_increase_per_wave": 0.03, "late_increase_per_wave": 0.05, "max_cost_multiplier": 3.0}, "dynamic_cost_increase": {"per_tower_built_multiplier": 0.15, "max_per_tower_multiplier": 20}}, "enemy_buff_config": {"description": "Enemy buff system configuration for this scenario", "enabled": true, "scenario_type": "standard", "buff_intensity": "medium", "custom_spawn_rates": {"wave_ranges": {"1-10": {"base_chance": 0.08, "max_buffs": 1, "allowed_buffs": ["speed_boost", "armor"]}, "11-20": {"base_chance": 0.18, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration"]}, "21-30": {"base_chance": 0.28, "max_buffs": 3, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]}, "31-40": {"base_chance": 0.38, "max_buffs": 4, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance"]}, "41-50": {"base_chance": 0.48, "max_buffs": 5, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}, "51+": {"base_chance": 0.65, "max_buffs": 6, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}}, "boss_multipliers": {"mini_boss": 2.2, "boss": 3.2, "super_boss": 4.5}}, "featured_combinations": ["stealth_assassin", "flying_fortress", "regenerating_tank"], "buff_metrics_tracking": {"track_buff_effectiveness": true, "track_tower_counters": true, "track_player_adaptation": true}}, "balance_config": {"currency": {"damage_divisor": 40, "utility_hit_reward": 1, "detector_reward_per_enemy": 2, "detector_reward_interval": 60, "firing_reward": 1}, "counter_system": {"tower_enemy_multipliers": {"antiair": {"FlyingEnemy": 2.5, "InvisibleEnemy": 1.8}, "cannon": {"ArmoredEnemy": 2.0, "TankEnemy": 1.8, "ShieldedEnemy": 1.5}, "sniper": {"FastEnemy": 2.0, "InvisibleEnemy": 1.8, "TeleportingEnemy": 1.5, "AdaptiveEnemy": 2.0}, "poison": {"RegeneratingEnemy": 2.5, "ToxicMutantEnemy": 2.0, "BasicEnemy": 1.3}, "flame": {"ArmoredEnemy": 1.5, "ToxicMutantEnemy": 2.0, "PhaseShiftEnemy": 1.8}, "ice": {"FastEnemy": 2.0, "FireElementalEnemy": 3.0, "AdaptiveEnemy": 2.0, "FlyingEnemy": 1.5}, "laser": {"EnergyShieldEnemy": 0.5, "CrystallineEnemy": 3.0, "ArmoredEnemy": 1.8, "BlastProofEnemy": 2.0}, "lightning": {"GroundedEnemy": 0.3, "SpectralEnemy": 3.0, "EnergyShieldEnemy": 2.0, "wet_enemies": 2.0}, "missile": {"VoidEnemy": 2.5, "BlastProofEnemy": 0.5, "FlyingEnemy": 2.0, "SplittingEnemy": 1.8}, "explosive": {"VoidEnemy": 2.5, "BlastProofEnemy": 0.3, "SplittingEnemy": 2.0, "TeleportingEnemy": 1.5}, "splash": {"SplittingEnemy": 2.2, "TeleportingEnemy": 1.8, "RegeneratingEnemy": 1.5}, "freezer": {"FastEnemy": 2.5, "FireElementalEnemy": 2.0, "TeleportingEnemy": 1.8}}, "default_multiplier": 1.0, "max_multiplier": 3.0, "min_multiplier": 0.1}, "immunity": {"base_chance_per_wave": 0.01, "max_immunity_chance": 0.15, "boss_wave_multiplier": 2.0, "mini_boss_multiplier": 1.5, "early_game_waves": 10, "early_game_max_immunities": 2}, "freeze": {"slow_factor": 0.25, "resistance_duration_multiplier": 0.5, "resistance_slow_factor": 0.6}}, "_variant_metadata": {"base_level": "tower_defense_game", "variant_id": "tower_defense_game_budget_crisis", "variant_name": "Tower_Defense_Game: Budget Crisis", "modifiers": [{"id": "budget_crisis", "name": "Budget Crisis", "description": "Start with 50% less money than usual", "modifier_type": "economic", "difficulty": "hard", "difficulty_multiplier": 1.4, "reward_multiplier": 1.6, "fun_factor": 9, "config_changes": {"starting_money_multiplier": 0.5}, "player_visible": true, "prerequisites": null, "conflicts": null}], "base_difficulty": 70, "final_difficulty": 98, "difficulty_multiplier": 1.4, "reward_multiplier": 1.6, "generation_timestamp": "2025-07-09T10:32:18.119340", "estimated_playtime": "30-39 minutes"}}