"""
Navigation Manager for Tower Defense Game Launcher
Handles view navigation and state management
"""

from typing import Dict, List, Any, Optional


class NavigationManager:
    """Manages navigation between different launcher views and states"""
    
    def __init__(self):
        """Initialize the navigation manager"""
        # State
        self.selected_config = None
        self.scroll_offset = 0
        self.max_scroll = 0
        self.current_view = "main"  # "main", "variants", or "level_options"
        self.selected_base_level = None  # Which base level's variants we're viewing
        self.level_options_config = None  # Config being shown in level options view
        
        # UI state
        self.show_generation_status = False
        self.generation_message = ""
        self.show_performance_panel = False
        self.show_upgrade_menu = False
        self.show_variant_selector = False
        self.pending_variant_creation = None
        
        # Level preview system  
        self.show_level_preview = False
        self.preview_config = None
        self.preview_scroll_offset = 0
        self.preview_max_scroll = 0
    
    def update_scroll_limits(self, configs_count: int):
        """Update scroll limits based on current view"""
        cards_per_row = 3
        rows_visible = 4
        cards_visible = cards_per_row * rows_visible
        
        total_configs = configs_count
        if self.current_view == "variants":
            total_configs += 1  # +1 for back button
        
        if total_configs > cards_visible:
            self.max_scroll = total_configs - cards_visible
        else:
            self.max_scroll = 0
    
    def navigate_to_variants(self, base_level_name: str):
        """Navigate to variants view for a specific base level"""
        self.current_view = "variants"
        self.selected_base_level = base_level_name
        self.selected_config = None
        self.scroll_offset = 0
    
    def navigate_to_main(self):
        """Navigate back to main view (base levels)"""
        self.current_view = "main"
        self.selected_base_level = None
        self.level_options_config = None
        self.selected_config = None
        self.scroll_offset = 0
    
    def navigate_to_level_options(self, config):
        """Navigate to level options view (play original vs view variants)"""
        self.current_view = "level_options"
        self.level_options_config = config
        self.selected_config = None
        self.scroll_offset = 0
    
    def update_scroll(self):
        """Update scroll offset to keep selected item visible"""
        if self.selected_config is None:
            return
        
        visible_items = 8
        
        # If selected item is above visible area
        if self.selected_config < self.scroll_offset:
            self.scroll_offset = self.selected_config
        
        # If selected item is below visible area
        elif self.selected_config >= self.scroll_offset + visible_items:
            self.scroll_offset = self.selected_config - visible_items + 1
        
        # Clamp scroll offset
        self.scroll_offset = max(0, min(self.max_scroll, self.scroll_offset))
    

    
    def close_overlays(self):
        """Close all overlay windows"""
        self.show_variant_selector = False
        self.show_level_preview = False
        self.preview_config = None
        self.show_upgrade_menu = False
        self.show_performance_panel = False
    
    def show_status_message(self, message: str):
        """Show a status message"""
        self.generation_message = message
        self.show_generation_status = True
    
    def clear_status_message(self):
        """Clear the status message"""
        self.show_generation_status = False
        self.generation_message = ""
    
    def open_level_preview(self, config):
        """Open level preview for a config"""
        self.show_level_preview = True
        self.preview_config = config
        self.preview_scroll_offset = 0
    
    def close_level_preview(self):
        """Close level preview"""
        self.show_level_preview = False
        self.preview_config = None
    
    def toggle_performance_panel(self):
        """Toggle the performance panel"""
        self.show_performance_panel = not self.show_performance_panel
    
    def toggle_upgrade_menu(self):
        """Toggle the upgrade menu"""
        self.show_upgrade_menu = not self.show_upgrade_menu
    
    def open_variant_selector(self):
        """Open the variant selector"""
        self.show_variant_selector = True
    
    def close_variant_selector(self):
        """Close the variant selector"""
        self.show_variant_selector = False
